"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building, Globe, Lock, MapPin, Star } from "lucide-react";
import StartupContactCard from "./StartupContactCard";
import { Progress } from "@/components/ui/progress";

export interface StartupContact {
  id: string;
  startup: string;
  website?: string;
  hq?: string;
  tags?: string;
  company_description?: string;
  first_contact_role?: string;
  first_contact_email?: string | null;
  first_contact_first_name?: string;
  first_contact_last_name?: string;
  second_contact_role?: string;
  second_contact_email?: string | null;
  second_contact_first_name?: string;
  second_contact_last_name?: string;
}

interface StartupCardProps {
  contact: StartupContact;
  unlocked: boolean;
  onUnlock: (contact: StartupContact) => void;
  onSelect: (selectedContact: {
    email: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    startup: string;
    website?: string;
    hq?: string;
    startupId: string;
  }) => void;
  freeSelectionUsed?: boolean;
  secondContactUnlocked?: boolean;
  matchScore?: number; // value between 0 and 1
}

const StartupCard: React.FC<StartupCardProps> = ({
  contact,
  unlocked,
  onUnlock,
  onSelect,
  freeSelectionUsed = false,
  secondContactUnlocked = false,
  matchScore,
}) => {
  // Calculate displayed score and numeric value for a progress bar.
  const displayedScore = matchScore !== undefined ? (matchScore * 100).toFixed(1) : "N/A";
  const numericMatchScore = matchScore ? matchScore * 100 : 0;

  const [showContactInfoInternal, setShowContactInfoInternal] = useState(unlocked);
  const [selectedContactEmail, setSelectedContactEmail] = useState<string | null>(null);
  const [isFirstContactSelected, setIsFirstContactSelected] = useState(false);
  const [isSecondContactSelected, setIsSecondContactSelected] = useState(false);
  const [internalSecondContactUnlocked, setInternalSecondContactUnlocked] = useState(
    secondContactUnlocked || unlocked
  );
  const [hasUsedFreeSelection, setHasUsedFreeSelection] = useState(freeSelectionUsed);

  useEffect(() => {
    setShowContactInfoInternal(unlocked);
  }, [unlocked]);

  useEffect(() => {
    setIsFirstContactSelected(freeSelectionUsed);
  }, [freeSelectionUsed]);

  useEffect(() => {
    setInternalSecondContactUnlocked(secondContactUnlocked || unlocked);
  }, [secondContactUnlocked, unlocked]);

  if (!contact.startup) return null;

  const getInitials = (name: string) => name.charAt(0).toUpperCase();

  const handleUnlock = () => {
    onUnlock(contact);
  };

  const handleContactSelect = (selectedContact: {
    email: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    startup: string;
    website?: string;
    hq?: string;
    startupId: string;
  }) => {
    if (!hasUsedFreeSelection) {
      setSelectedContactEmail(selectedContact.email);
      setHasUsedFreeSelection(true);
    }
    if (selectedContact.email === contact.first_contact_email) {
      setIsFirstContactSelected(true);
    } else if (selectedContact.email === contact.second_contact_email) {
      setIsSecondContactSelected(true);
    }
    onSelect(selectedContact);
  };

  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md border-blue-100 relative bg-white">
      <CardContent className="p-0">
        <div className="flex flex-col">
          {/* Header Section */}
          <div className="flex items-center p-4 border-b border-blue-100 bg-blue-50">
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center shrink-0 border-2 border-white shadow-sm">
              <span className="text-blue-700 font-medium text-xl">
                {getInitials(contact.startup)}
              </span>
            </div>
            <div className="ml-4 flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {contact.startup}
                </h3>
                {/* Render match score if defined */}
                {matchScore !== undefined && (
                  <div className="flex items-center gap-2">
                    <Badge className="bg-blue-600 text-white">
                      {displayedScore}% Match
                    </Badge>
                    <Star className="h-4 w-4 text-yellow-400" fill="currentColor" />
                  </div>
                )}
              </div>
              <div className="flex flex-wrap items-center gap-3 mt-1 text-sm text-gray-600">
                {contact.website && (
                  <a
                    href={contact.website.startsWith("http") ? contact.website : `https://${contact.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    <Globe className="h-3.5 w-3.5 mr-1" />
                    <span className="truncate">{contact.website.replace(/^https?:\/\//, '')}</span>
                  </a>
                )}
                {contact.hq && (
                  <span className="flex items-center">
                    <MapPin className="h-3.5 w-3.5 mr-1 text-blue-500" />
                    <span className="truncate">{contact.hq}</span>
                  </span>
                )}
              </div>
              {/* Optionally, add a progress bar below the header */}
              {matchScore !== undefined && (
                <div className="mt-2">
                  <Progress
                  value={numericMatchScore}
                  className="h-1.5 w-full bg-blue-200"
                  style={{ '--progress-fill': '#00FF00' } as React.CSSProperties}
                />
                </div>
              )}
            </div>
          </div>

          {/* Content Section */}
          <div className="p-4">
            {contact.tags && (
              <div className="flex flex-wrap gap-2 mb-3">
                {contact.tags.split(",").map((tag, i) => (
                  <Badge
                    key={i}
                    variant="outline"
                    className="text-blue-700 border-blue-200 bg-blue-50 hover:bg-blue-100"
                  >
                    {tag.trim()}
                  </Badge>
                ))}
              </div>
            )}
            {contact.company_description && (
              <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                {contact.company_description}
              </p>
            )}
            {showContactInfoInternal ? (
              <div className="animate-fade-in">
                <h4 className="text-sm font-medium text-gray-800 mb-3 flex items-center">
                  <Building className="h-4 w-4 mr-2 text-blue-600" />
                  Contact Information
                </h4>
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                  {contact.first_contact_email && (
                    <StartupContactCard
                      contact={{
                        email: contact.first_contact_email,
                        firstName: contact.first_contact_first_name,
                        lastName: contact.first_contact_last_name,
                        role: contact.first_contact_role,
                        startup: contact.startup,
                        website: contact.website,
                        hq: contact.hq,
                        startupId: contact.id,
                      }}
                      onSelect={handleContactSelect}
                      requiresToken={
                        hasUsedFreeSelection &&
                        selectedContactEmail !== contact.first_contact_email
                      }
                      isSelected={isFirstContactSelected}
                      isUnlocked={
                        !hasUsedFreeSelection ||
                        selectedContactEmail === contact.first_contact_email ||
                        internalSecondContactUnlocked
                      }
                      firstContactSelected={hasUsedFreeSelection}
                    />
                  )}
                  {contact.second_contact_email && (
                    <StartupContactCard
                      contact={{
                        email: contact.second_contact_email,
                        firstName: contact.second_contact_first_name,
                        lastName: contact.second_contact_last_name,
                        role: contact.second_contact_role,
                        startup: contact.startup,
                        website: contact.website,
                        hq: contact.hq,
                        startupId: contact.id,
                      }}
                      onSelect={handleContactSelect}
                      requiresToken={
                        hasUsedFreeSelection &&
                        selectedContactEmail !== contact.second_contact_email
                      }
                      isSelected={isSecondContactSelected}
                      isUnlocked={
                        !hasUsedFreeSelection ||
                        selectedContactEmail === contact.second_contact_email ||
                        internalSecondContactUnlocked
                      }
                      firstContactSelected={hasUsedFreeSelection}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <Lock className="h-4 w-4 text-blue-600 mr-2" />
                  <span className="text-sm text-gray-700">Unlock to view contacts</span>
                </div>
                <Button onClick={handleUnlock} size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                  Unlock (1 Token)
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StartupCard;
