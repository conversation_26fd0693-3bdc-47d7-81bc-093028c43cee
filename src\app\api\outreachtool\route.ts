// File: /app/api/outreachtool/route.ts
import { NextResponse } from "next/server";
import { google } from "googleapis";
import { createClient } from "@supabase/supabase-js";

interface EmailAttachment {
  filename: string;
  mimeType: string;
  content: string; // base64-encoded content
}

interface EmailRequestBody {
  recipient: string;
  subject: string;
  message: string;
  googleAccessToken?: string;
  attachments?: EmailAttachment[];
}

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
);

export async function POST(request: Request) {
  try {
    const body: EmailRequestBody = await request.json();
    const { recipient, subject, message, googleAccessToken, attachments } = body;

    if (!recipient || !subject || !message) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }

    if (!googleAccessToken) {
      return NextResponse.json({ error: "No Google token provided" }, { status: 400 });
    }

    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Missing or invalid Supabase token" }, { status: 401 });
    }

    const supabaseToken = authHeader.split("Bearer ")[1];
    const { data: { user }, error: userError } = await supabase.auth.getUser(supabaseToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Invalid Supabase auth" }, { status: 401 });
    }

    const oauth2Client = new google.auth.OAuth2(
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI
    );

    oauth2Client.setCredentials({ access_token: googleAccessToken });

    const gmail = google.gmail({ version: "v1", auth: oauth2Client });

    const boundary = "klinn_boundary";

    const attachmentParts = attachments?.map(att => `
--${boundary}
Content-Type: ${att.mimeType}; name="${att.filename}"
Content-Disposition: attachment; filename="${att.filename}"
Content-Transfer-Encoding: base64

${att.content}`).join('') || '';

    const emailLines = [
      `To: ${recipient}`,
      `Subject: ${subject}`,
      `MIME-Version: 1.0`,
      `Content-Type: multipart/mixed; boundary="${boundary}"`,
      "",
      `--${boundary}`,
      `Content-Type: text/html; charset="UTF-8"`,
      `Content-Transfer-Encoding: 7bit`,
      "",
      message,
      attachmentParts,
      `--${boundary}--`
    ];

    const email = emailLines.join("\r\n");

    const encodedEmail = Buffer.from(email)
      .toString("base64")
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=+$/, "");

    const result = await gmail.users.messages.send({
      userId: "me",
      requestBody: { raw: encodedEmail },
    });

    return NextResponse.json({ success: true, messageId: result.data.id });
  } catch (error: any) {
    console.error("Outreach error:", error);
    if (error.code === 401) {
      return NextResponse.json({ error: "Google auth expired", code: "auth_expired" }, { status: 401 });
    }
    return NextResponse.json({ error: error.message || "Server error" }, { status: 500 });
  }
}
