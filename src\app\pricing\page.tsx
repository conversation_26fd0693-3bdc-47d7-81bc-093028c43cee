'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Footer from '@/components/Footer';
import { CheckCircle, X, Sparkles, ArrowRight, Shield, Clock, Zap, UserCheck, Building, Briefcase, Award, Rocket, Flag } from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import PricingToggle from '@/components/PricingToggle';

const fadeInUpVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerChildren = {
  visible: {
    transition: {
      staggerChildren: 0.2
    }
  }
};

interface PlanFeature {
  included: boolean;
  text: string;
  comingSoon?: boolean;
  category?: string;
}

interface Plan {
  name: string;
  description: string;
  price: string;
  yearlyPrice: string;
  billingPeriod: string;
  yearlyBillingPeriod?: string;
  features: PlanFeature[];
  popularPlan: boolean;
  ctaText: string;
  stripeLink?: string;
  monthlyStripeLink?: string;
  yearlyStripeLink?: string;
  bgColor: string;
  icon: React.ComponentType<any>;
}

const plans: Plan[] = [
  {
    name: "Free",
    description: "Basic features to get started",
    price: "$0",
    yearlyPrice: "$0",
    billingPeriod: "forever",
    features: [
      { included: true, text: "1 AI Generated Cold Call Script" },
      { included: true, text: "5 Tokens per month" },
      { included: true, text: "30 Displayed Nonmed ECs" },
      { included: true, text: "30 Displayed Medical ECs" },
      { included: true, text: "Quick Apply to up to 5 jobs" },
      { included: false, text: "AI Resume Scorer" },
      { included: false, text: "Priority Applicant Status" }
    ],
    popularPlan: false,
    ctaText: "Get Started",
    stripeLink: "https://klinn.works",
    bgColor: "from-blue-400 to-blue-500",
    icon: Clock
  },
  {
    name: "Basic",
    description: "Enhanced features for students",
    price: "$4.99",
    yearlyPrice: "$47.99",
    billingPeriod: "per month",
    yearlyBillingPeriod: "per year",
    features: [
      { included: true, text: "3 AI Generated Cold Call Scripts per month" },
      { included: true, text: "15 Tokens per month" },
      { included: true, text: "60 Displayed Nonmed ECs" },
      { included: true, text: "60 Displayed Medical ECs" },
      { included: true, text: "Quick Apply to 30 jobs per month" },
      { included: true, text: "AI Resume Scorer (2x/month)" },
      { included: true, text: "Early Access to Job Postings (24h)" }
    ],
    popularPlan: false,
    ctaText: "Choose Basic",
    monthlyStripeLink: "https://buy.stripe.com/test_5kA7vrgQPbGgf8Q9AA",
    yearlyStripeLink: "https://buy.stripe.com/test_aEU8zvbwv5hSe4McMP",
    bgColor: "from-blue-500 to-blue-600",
    icon: Shield
  },
  {
    name: "Premium",
    description: "Complete toolkit for healthcare students",
    price: "$9.99",
    yearlyPrice: "$95.99",
    billingPeriod: "per month",
    yearlyBillingPeriod: "per year",
    features: [
      { included: true, text: "10 AI Generated Cold Call Scripts per month" },
      { included: true, text: "50 Tokens per month" },
      { included: true, text: "All Displayed Medical & Nonmed ECs" },
      { included: true, text: "Apply to unlimited jobs" },
      { included: true, text: "Custom Profile Banner", comingSoon: true },
      { included: true, text: "Priority Applicant Status", comingSoon: true },
      { included: true, text: "AI Resume Scorer (5x/month)", comingSoon: true },
      { included: true, text: "AI Job & Extracurricular Matching", comingSoon: true }
    ],
    popularPlan: true,
    ctaText: "Choose Premium",
    monthlyStripeLink: "https://buy.stripe.com/test_eVaaHD2ZZ6lW1i0001",
    yearlyStripeLink: "https://buy.stripe.com/test_4gw8zv5877q09Ow5km",
    bgColor: "from-blue-600 to-blue-700",
    icon: Rocket
  },
  {
    name: "Enterprise",
    description: "Custom solutions for institutions",
    price: "Custom",
    yearlyPrice: "Custom",
    billingPeriod: "pricing",
    yearlyBillingPeriod: "pricing",
    features: [
      { included: true, text: "Access to multiple accounts" },
      { included: true, text: "Higher cap on AI features" },
      { included: true, text: "Institution-wide analytics" },
      { included: true, text: "Perfect for high schools, colleges, and counselors" },
      { included: true, text: "Dedicated account manager" },
      { included: true, text: "Bulk account management" },
      { included: true, text: "Track student performance" }
    ],
    popularPlan: false,
    ctaText: "Contact Sales",
    stripeLink: "https://klinn.works",
    bgColor: "from-blue-800 to-blue-900",
    icon: Building
  }
];

const PricingPage = () => {
  const [billingCycle, setBillingCycle] = useState('monthly');
  
  useEffect(() => {
    document.title = 'Klinn | Pricing Plans';
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-40 pb-32 bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 relative overflow-hidden">
        {/* <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div> */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div 
            className="max-w-4xl mx-auto text-center text-white"
            initial="hidden"
            animate="visible"
            variants={staggerChildren}
          >
            <motion.span
              className="inline-block px-4 py-1 mb-8 text-xs font-extralight tracking-wider uppercase bg-white/10 rounded-full backdrop-blur-sm border border-white/10"
              variants={fadeInUpVariants}
            >
              Pricing
            </motion.span>
            <motion.h1 
              className="text-5xl sm:text-6xl font-extralight mb-8 leading-tight tracking-tight"
              variants={fadeInUpVariants}
            >
              Choose the Perfect Plan for Your <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-100 to-blue-200 font-light">Healthcare Journey</span>
            </motion.h1>
            <motion.p 
              className="text-xl mb-12 opacity-90 max-w-2xl mx-auto font-extralight tracking-wide"
              variants={fadeInUpVariants}
            >
              Access powerful tools designed to advance your healthcare education and career goals
            </motion.p>
            
            <motion.div 
              variants={fadeInUpVariants}
              className="flex justify-center"
            >
              <Button 
                className="bg-white/90 text-blue-600 hover:bg-white rounded-full px-8 py-6 font-extralight tracking-wide shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
                onClick={() => window.location.href = '#pricing'}
              >
                View Plans
                <ArrowRight className="ml-2 h-5 w-5 opacity-70" />
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
      
      {/* Main Content */}
      <main className="flex-grow bg-white">
        {/* Pricing Section */}
        <section id='pricing' className="py-24">
          <div className="container mx-auto px-4 relative z-10">
            <motion.div 
              className="max-w-2xl mx-auto text-center mb-16"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.h2 
                className="text-3xl font-extralight mb-5 text-gray-800 tracking-tight"
                variants={fadeInUpVariants}
              >
                Pricing
              </motion.h2>
              <motion.p 
                className="text-lg text-gray-500 font-extralight mb-10 tracking-wide"
                variants={fadeInUpVariants}
              >
                Choose the plan that fits your needs with no hidden fees
              </motion.p>
              
              <motion.div 
                variants={fadeInUpVariants}
                className="flex justify-center"
              >
                <div 
                  className="bg-gray-50 p-1 rounded-full flex items-center shadow-sm border border-gray-100 cursor-pointer" 
                  onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
                >
                  <div className={`px-6 py-2 rounded-full text-sm font-extralight transition-all duration-200 relative ${billingCycle === 'monthly' ? 'bg-blue-600 text-white shadow-sm' : 'text-gray-500'}`}>
                    Monthly
                  </div>
                  <div className={`px-6 py-2 rounded-full text-sm font-extralight transition-all duration-200 relative ${billingCycle === 'yearly' ? 'bg-blue-600 text-white shadow-sm' : 'text-gray-500'}`}>
                    Yearly
                    <span className={`text-xs font-light ml-1 ${billingCycle === 'yearly' ? 'text-blue-300' : 'text-blue-500'}`}>Save 20%</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
              {plans.map((plan, index) => (
                <motion.div
                  key={index}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={fadeInUpVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className={`h-full flex flex-col border-0 shadow-md hover:shadow-lg transition-all duration-300 ${plan.popularPlan ? 'ring-1 ring-blue-300' : ''} relative overflow-hidden rounded-xl`}>
                    {plan.popularPlan && (
                      <div className="absolute -right-12 top-7 rotate-45 bg-blue-500 text-white text-center py-1 px-12 text-xs font-extralight tracking-wider z-20 shadow-sm">
                        MOST POPULAR
                      </div>
                    )}
                    
                    <div className={`bg-gradient-to-r ${plan.bgColor} p-8 h-64 relative flex flex-col justify-between`}>
                      <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-bl-full"></div>
                      <div>
                        <div className="flex items-center mb-3">
                          <div className="w-9 h-9 bg-white/10 rounded-full flex items-center justify-center mr-3 backdrop-blur-sm">
                            <plan.icon className="w-4 h-4 text-white opacity-80" />
                          </div>
                          <CardTitle className="text-lg font-extralight text-white tracking-wide">{plan.name}</CardTitle>
                        </div>
                        <CardDescription className="text-blue-100 text-sm font-extralight tracking-wide opacity-90">{plan.description}</CardDescription>
                      </div>
                      
                      <div className="mt-auto">
                        <span className="text-4xl font-extralight text-white tracking-tight">
                          {billingCycle === 'monthly' ? plan.price : plan.yearlyPrice}
                        </span>
                        <span className="text-blue-100 ml-1 text-sm font-extralight">
                          {billingCycle === 'monthly' ? plan.billingPeriod : plan.yearlyBillingPeriod || plan.billingPeriod}
                        </span>
                        {billingCycle === 'yearly' && plan.name !== 'Free' && plan.name !== 'Enterprise' && (
                          <div className="text-xs mt-1 text-blue-100 font-extralight tracking-wide">Save ~20% with annual billing</div>
                        )}
                      </div>
                    </div>
                    
                    <CardContent className="p-8 px-10 flex-grow">
                      <h4 className="text-xs font-extralight text-gray-500 mb-5 uppercase tracking-widest">Features</h4>
                      
                      <ul className="space-y-4 mb-6">
                        {plan.features.map((feature, i) => (
                          <li key={i} className="flex items-start text-sm">
                            {feature.included ? (
                              <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0 mr-3 mt-0.5" />
                            ) : (
                              <X className="h-4 w-4 text-gray-300 flex-shrink-0 mr-3 mt-0.5" />
                            )}
                            <span className={`${feature.included ? "text-gray-600" : "text-gray-400"} flex-grow font-extralight tracking-wide`}>
                              {feature.text}
                              {feature.comingSoon && (
                                <span className="ml-1 text-xs font-extralight text-blue-400 inline-block tracking-wide"> (Coming Soon)</span>
                              )}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    
                    <CardFooter className="px-10 pb-8 pt-0 mt-auto">
                      <Button 
                        className={`w-full rounded-full text-sm h-11 font-extralight tracking-wide shadow-sm hover:shadow-md transition-all duration-300 ${plan.popularPlan 
                          ? 'bg-blue-600 hover:bg-blue-700' 
                          : 'bg-blue-500 hover:bg-blue-600'}`}
                        onClick={() => {
                          if (plan.stripeLink) {
                            window.location.href = plan.stripeLink;
                          } else if (billingCycle === 'monthly' && plan.monthlyStripeLink) {
                            window.location.href = plan.monthlyStripeLink;
                          } else if (billingCycle === 'yearly' && plan.yearlyStripeLink) {
                            window.location.href = plan.yearlyStripeLink;
                          }
                        }}
                      >
                        {plan.ctaText}
                        <ArrowRight className="ml-1 h-4 w-4 opacity-70" />
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        
        {/* Features Comparison */}
        <section id='comparison' className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-3xl font-extralight mb-12 text-center text-gray-800 tracking-tight"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Plan Comparison
            </motion.h2>
            
            <div className="overflow-x-auto rounded-xl shadow-md">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                className="min-w-max"
              >
                <table className="w-full bg-white rounded-xl overflow-hidden">
                  <thead>
                    <tr className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                      <th className="py-4 px-8 text-left font-extralight text-sm tracking-wide">Feature</th>
                      <th className="py-4 px-8 text-center font-extralight text-sm tracking-wide">Free</th>
                      <th className="py-4 px-8 text-center font-extralight text-sm tracking-wide">Basic</th>
                      <th className="py-4 px-8 text-center font-extralight text-sm tracking-wide">Premium</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { feature: "AI Cold Call Scripts", free: "1 total", basic: "3/month", premium: "10/month" },
                      { feature: "Tokens", free: "5/month", basic: "15/month", premium: "50/month" },
                      { feature: "Displayed Nonmed ECs", free: "30", basic: "60", premium: "All" },
                      { feature: "Displayed Medical ECs", free: "30", basic: "60", premium: "All" },
                      { feature: "Job Applications", free: "5 jobs", basic: "30/month", premium: "Unlimited" },
                      { feature: "Custom Profile Banner", free: "✗", basic: "✗", premium: "✓ (Coming Soon)" },
                      { feature: "AI Resume Scorer", free: "✗", basic: "2x/month", premium: "5x/month (Coming Soon)" },
                      { feature: "Priority Applicant Status", free: "✗", basic: "✗", premium: "✓ (Coming Soon)" },
                      { feature: "Early Access to Jobs", free: "✗", basic: "24 hours", premium: "48 hours" },
                      { feature: "AI Job & EC Matching", free: "✗", basic: "✗", premium: "✓ (Coming Soon)" },
                    ].map((row, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                        <td className="py-3 px-8 text-gray-600 text-sm font-extralight tracking-wide">{row.feature}</td>
                        <td className="py-3 px-8 text-center text-gray-500 text-sm font-extralight tracking-wide">{row.free}</td>
                        <td className="py-3 px-8 text-center text-gray-500 text-sm font-extralight tracking-wide">{row.basic}</td>
                        <td className="py-3 px-8 text-center text-gray-500 text-sm font-extralight tracking-wide">{row.premium}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </motion.div>
            </div>
          </div>
        </section>
        
        {/* Call to Action */}
        <section className="py-24 bg-gray-50 text-blue-600">
          <motion.div 
            className="container mx-auto px-4 text-center"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 
              className="text-3xl font-extralight mb-5 tracking-tight"
              variants={fadeInUpVariants}
            >
              Start Your Healthcare Journey Today
            </motion.h2>
            <motion.p 
              className="text-lg mb-10 max-w-xl mx-auto opacity-90 font-extralight tracking-wide"
              variants={fadeInUpVariants}
            >
              Join thousands of students achieving their goals with Klinn
            </motion.p>
            <motion.div 
              className="flex flex-wrap justify-center gap-5"
              variants={fadeInUpVariants}
            >
              <Button 
                className="bg-blue-600 text-white hover:bg-white hover:text-blue-600 rounded-full px-6 py-2 font-extralight tracking-wide shadow-md hover:shadow-lg transition-all duration-300"
                onClick={() => window.location.href = '#pricing'}
              >
                Try Premium
                <ArrowRight className="ml-1 h-4 w-4 opacity-70" />
              </Button>
              <Button 
                variant="outline"
                className="bg-transparent border-blue-600 text-blue-600 hover:bg-white/10 rounded-full px-6 py-2 font-extralight tracking-wide hover:shadow-md transition-all duration-300"
                onClick={() => window.location.href = '#comparison'}
              >
                Compare Plans
              </Button>
            </motion.div>
          </motion.div>
        </section>
      </main>

      <Footer className="bg-blue-600 text-white py-10" />
    </div>
  );
};

export default PricingPage;
