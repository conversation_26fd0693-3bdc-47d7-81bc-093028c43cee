import { NextRequest, NextResponse } from "next/server";
import { getUserSubscription, hasAccessToFeature, getFeatureLimit } from "@/lib/subscription";
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const getSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabaseAccessToken = cookieStore.get('sb-access-token')?.value;
    
    if (!supabaseAccessToken) {
      console.error('No Supabase access token found in cookies');
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const supabase = getSupabaseClient();
    
    const { data: { user }, error: userError } = await supabase.auth.getUser(supabaseAccessToken);
    
    if (userError || !user) {
      console.error('Error getting user from token:', userError);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = user.id;
    const url = new URL(req.url);
    const feature = url.searchParams.get("feature");

    const subscription = await getUserSubscription(userId);
    
    if (feature) {
      const hasAccess = hasAccessToFeature(subscription, feature);
      const limit = getFeatureLimit(subscription, feature);
      
      return NextResponse.json({
        hasAccess,
        limit,
        planType: subscription?.planType || "free",
        status: subscription?.status || "inactive",
      });
    }

    return NextResponse.json({
      subscription: {
        planType: subscription?.planType || "free",
        status: subscription?.status || "inactive",
        currentPeriodEnd: subscription?.currentPeriodEnd,
        features: {
          coldCallScripts: {
            hasAccess: hasAccessToFeature(subscription, 'coldCallScripts'),
            limit: getFeatureLimit(subscription, 'coldCallScripts'),
          },
          tokens: {
            hasAccess: hasAccessToFeature(subscription, 'tokens'),
            limit: getFeatureLimit(subscription, 'tokens'),
          },
          nonmedECs: {
            hasAccess: hasAccessToFeature(subscription, 'nonmedECs'),
            limit: getFeatureLimit(subscription, 'nonmedECs'),
          },
          medicalECs: {
            hasAccess: hasAccessToFeature(subscription, 'medicalECs'),
            limit: getFeatureLimit(subscription, 'medicalECs'),
          },
          jobApplications: {
            hasAccess: hasAccessToFeature(subscription, 'jobApplications'),
            limit: getFeatureLimit(subscription, 'jobApplications'),
          },
          aiResumeScorer: {
            hasAccess: hasAccessToFeature(subscription, 'aiResumeScorer'),
            limit: getFeatureLimit(subscription, 'aiResumeScorer'),
          },
          priorityApplicant: {
            hasAccess: hasAccessToFeature(subscription, 'priorityApplicant'),
          },
          customProfileBanner: {
            hasAccess: hasAccessToFeature(subscription, 'customProfileBanner'),
          },
          earlyAccessJobs: {
            hasAccess: hasAccessToFeature(subscription, 'earlyAccessJobs'),
            limit: getFeatureLimit(subscription, 'earlyAccessJobs'),
          },
          aiJobMatching: {
            hasAccess: hasAccessToFeature(subscription, 'aiJobMatching'),
          },
          automatedEmails: {
            hasAccess: hasAccessToFeature(subscription, 'automatedEmails'),
            limit: getFeatureLimit(subscription, 'automatedEmails'),
          },
          maxEmails: {
            limit: getFeatureLimit(subscription, 'maxEmails'),
          },
          newsletter: {
            hasAccess: hasAccessToFeature(subscription, 'newsletter'),
          },
        }
      },
    });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscription data", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 