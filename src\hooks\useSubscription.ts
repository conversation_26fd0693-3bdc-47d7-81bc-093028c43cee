import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SubscriptionPlan } from '@/lib/subscription';

type FeatureAccessResult = {
  hasAccess: boolean;
  limit: number;
  planType: SubscriptionPlan;
  status: string;
  isLoading: boolean;
  error: string | null;
};

export function useFeatureAccess(featureName: string): FeatureAccessResult {
  const supabase = createClientComponentClient();
  const [result, setResult] = useState<FeatureAccessResult>({
    hasAccess: false,
    limit: 0,
    planType: 'free',
    status: 'inactive',
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    let isMounted = true;
    
    const checkAccess = async () => {
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !session) {
          if (isMounted) {
            setResult({
              hasAccess: false,
              limit: 0,
              planType: 'free',
              status: 'inactive',
              isLoading: false,
              error: sessionError ? sessionError.message : 'No active session',
            });
          }
          return;
        }

        const response = await fetch(`/api/subscription?feature=${encodeURIComponent(featureName)}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch subscription data: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (isMounted) {
          setResult({
            hasAccess: data.hasAccess,
            limit: data.limit,
            planType: data.planType,
            status: data.status || 'inactive',
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error("Error in useFeatureAccess:", error);
        if (isMounted) {
          setResult({
            hasAccess: false,
            limit: 0,
            planType: 'free',
            status: 'inactive',
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    };

    checkAccess();
    
    return () => {
      isMounted = false;
    };
  }, [featureName, supabase.auth]);

  return result;
}

type SubscriptionData = {
  planType: SubscriptionPlan;
  status: string;
  currentPeriodEnd?: Date;
  features: Record<string, { hasAccess: boolean; limit?: number }>;
  isLoading: boolean;
  error: string | null;
};

export function useSubscription(): SubscriptionData {
  const supabase = createClientComponentClient();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    planType: 'free',
    status: 'inactive',
    isLoading: true,
    error: null,
    features: {},
  });

  useEffect(() => {
    let isMounted = true;
    
    const checkSubscription = async () => {
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !session) {
          if (isMounted) {
            setSubscriptionData({
              planType: 'free',
              status: 'inactive',
              isLoading: false,
              error: sessionError ? sessionError.message : 'No active session',
              features: {},
            });
          }
          return;
        }

        const response = await fetch('/api/subscription');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch subscription data: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (isMounted) {
          setSubscriptionData({
            planType: data.subscription.planType,
            status: data.subscription.status,
            currentPeriodEnd: data.subscription.currentPeriodEnd 
              ? new Date(data.subscription.currentPeriodEnd)
              : undefined,
            features: data.subscription.features || {},
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error("Error in useSubscription:", error);
        if (isMounted) {
          setSubscriptionData({
            planType: 'free',
            status: 'inactive',
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            features: {},
          });
        }
      }
    };

    checkSubscription();
    
    return () => {
      isMounted = false;
    };
  }, [supabase.auth]);

  return subscriptionData;
} 