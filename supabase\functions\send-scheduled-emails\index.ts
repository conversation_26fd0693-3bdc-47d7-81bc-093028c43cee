// supabase/functions/send-scheduled-emails/index.ts
import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { getAccessToken, gmailSend } from "../../functions/send-scheduled-emails/_shared/gmail.ts";
import { encodeBase64 } from "https://deno.land/std@0.224.0/encoding/base64.ts";

serve(async () => {
  console.log("Starting scheduled email processing job…");
  const startTime = Date.now();

  try {
    // 1) Initialize Supabase client
    const sb = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // 2) Fetch due email jobs
    const { data: jobs, error: fetchError } = await sb.rpc("fetch_due_email_jobs");

    if (fetchError) {
      console.error("Error fetching due email jobs:", fetchError);
      return new Response(JSON.stringify({ error: fetchError.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    console.log(`Found ${jobs?.length ?? 0} email(s) to process`);

    const results = {
      total: jobs?.length ?? 0,
      sent: 0,
      failed: 0,
      errors: {} as Record<string, string>,
    };

    for (const job of jobs ?? []) {
      console.log(`↳ processing job ${job.id} for user ${job.user_id}`);

      try {
        // 3-a) Get refresh token
        const { data: tok, error: tokenError } = await sb
          .from("user_oauth_tokens2")
          .select("refresh_token")
          .eq("user_id", job.user_id)
          .eq("provider", "google")
          .single();

        if (tokenError || !tok) {
          throw new Error(`No refresh token: ${tokenError?.message ?? "none"}`);
        }

        // 3-b) Mark job as processing
        await sb.from("scheduled_emails")
          .update({ status: "processing" })
          .eq("id", job.id);

        // 3-c) Get access token
        const access = await getAccessToken(tok.refresh_token);

        // 3-d) Prepare and encode attachments (if any)
        let encodedAttachments: Array<{
          filename: string;
          mimeType: string;
          content: string;
        }> = [];

        if (Array.isArray(job.attachments) && job.attachments.length > 0) {
          for (const att of job.attachments) {
            console.log("Downloading attachment:", att.path);
          
            const { data: fileBlob, error: dlErr } = await sb
              .storage
              .from("email-attachments")
              .download(att.path);
          
            if (dlErr || !fileBlob) {
              console.error("Download error:", dlErr);
              throw new Error(`Failed to download attachment ${att.name}: ${JSON.stringify(dlErr ?? {})}`);
            }
          
            const arrayBuffer = await fileBlob.arrayBuffer();
            const uint8 = new Uint8Array(arrayBuffer);
            const base64 = encodeBase64(uint8);
          
            encodedAttachments.push({
              filename: att.name,
              mimeType: att.mimeType,
              content: base64,
            });
          }          
        }

        console.log("Body length:", job.body?.length ?? "null");
        console.log("Attachments:", encodedAttachments.length);

        // 3-e) Send email via Gmail API
        const gmailId = await gmailSend({
          to_addresses: job.to_addresses,
          cc_addresses: job.cc_addresses || [],
          bcc_addresses: job.bcc_addresses || [],
          subject: job.subject,
          body: job.body,
          attachments: encodedAttachments,
          access,
        });

        // 3-f) Mark as sent
        await sb.from("scheduled_emails")
          .update({
            status: "sent",
            sent_at: new Date().toISOString(),
            gmail_msg_id: gmailId,
          })
          .eq("id", job.id);

        console.log(`✓ job ${job.id} sent (Gmail id ${gmailId})`);
        results.sent++;

      } catch (err) {
        const msg = err instanceof Error ? err.message : String(err);
        console.error(`✗ job ${job.id} failed:`, msg);

        await sb.from("scheduled_emails")
          .update({ status: "failed", error: msg })
          .eq("id", job.id);

        results.failed++;
        results.errors[job.id] = msg;
      }
    }

    const secs = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log(`Done – processed ${results.total} job(s) in ${secs}s`);

    return new Response(JSON.stringify(results), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });

  } catch (err) {
    console.error("Fatal error in worker:", err);
    return new Response(
      JSON.stringify({ error: err instanceof Error ? err.message : String(err) }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});
