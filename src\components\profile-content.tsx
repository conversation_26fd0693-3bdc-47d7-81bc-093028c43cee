"use client"

import type React from "react"
import type { ChangeEvent } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Mail,
  MapPin,
  GraduationCap,
  Globe,
  FileText,
  Upload,
  Download,
  Check,
  Trash2,
  Calendar,
  BookOpen,
  Building,
  Award,
  Info,
} from "lucide-react"
import { supabase } from "../../supabase/supabaseClient"
import { useRouter } from "next/navigation"

interface ProfileData {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  education: string;         // e.g. Degree type or education level
  current_school?: string;   // New field for where the user is currently studying
  location?: string;
  sat_act_score?: string;
  mcat_score?: string;
  bio?: string;
  website?: string;
  school_attended?: string;  // You may choose to keep this for past schools if needed
  // schools_accepted?: string;  // Removed field
  experience?: string;       // New field for work/internship experience
  projects?: string;         // New field for projects, portfolios, etc.
  skills?: string;           // New field for skills/interests, comma-separated or freeform text
  resumes?: Array<{
    name: string;
    url: string;
    uploaded_at: string;
  }>;
  default_resume_url?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  tokens: number;
}


interface ProfileContentProps {
  profileData: ProfileData
  setProfileData: React.Dispatch<React.SetStateAction<ProfileData | null>>
  isEditing: boolean
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>
  saveProfileData: () => Promise<void>
  resumes: ProfileData["resumes"] | undefined
  handleResumeUpload: (event: ChangeEvent<HTMLInputElement>) => Promise<void>
  handleRemoveResume: (resumeUrl: string) => Promise<void>
  handleSetDefaultResume: (resumeUrl: string) => Promise<void>
  activeTab?: string;
  onTabChange?: (newTab: string) => void;
}

export default function ProfileContent({
  profileData,
  setProfileData,
  isEditing,
  setIsEditing,
  saveProfileData,
  resumes,
  handleResumeUpload,
  handleRemoveResume,
  handleSetDefaultResume,
  activeTab = "personal", // default if not provided
  onTabChange,
}: ProfileContentProps) {
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setProfileData({ ...profileData, [name]: value })
  }
  
  const router = useRouter();

  const handleDeleteAccount = async () => {
    try {
      const response = await fetch("/api/delete-account", {
        method: "DELETE",
      })
      if (!response.ok) {
        console.error("Failed to delete account")
        return
      }
      // Sign out the user after successful deletion and redirect to home page.
      await supabase.auth.signOut()
      router.push("/")
    } catch (error) {
      console.error("Error deleting account:", error)
    }
  }

  return (
    <div className="space-y-6 w-full">
      <Tabs value={activeTab} onValueChange={(value) => onTabChange?.(value)} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4 w-full max-w-full">
          {/* Added id attributes for onboarding targeting */}
          <TabsTrigger 
            id="personal-tab" 
            value="personal" 
            className="flex items-center gap-1 sm:gap-2 px-2 py-1.5 sm:py-2 text-xs sm:text-sm"
          >
            <User className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate">Personal Info</span>
          </TabsTrigger>
          <TabsTrigger 
            id="education-tab"
            value="education" 
            className="flex items-center gap-1 sm:gap-2 px-2 py-1.5 sm:py-2 text-xs sm:text-sm"
          >
            <GraduationCap className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate">Education/Experience</span>
          </TabsTrigger>
          <TabsTrigger 
            id="resumes-tab"
            value="resumes" 
            className="flex items-center gap-1 sm:gap-2 px-2 py-1.5 sm:py-2 text-xs sm:text-sm"
          >
            <FileText className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate">Resumes</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <Info className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Basic Information
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Your personal details and contact information</CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <div className="grid gap-4 sm:gap-5 grid-cols-1 md:grid-cols-2">
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="first_name" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                    <User className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    First Name
                  </Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    placeholder="Enter your first name"
                    value={profileData.first_name}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="last_name" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                    <User className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Last Name
                  </Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    placeholder="Enter your last name"
                    value={profileData.last_name}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                    <Mail className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    placeholder="Email address"
                    disabled
                    value={profileData.email}
                    className="bg-gray-50 text-sm"
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="location" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                    <MapPin className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Location
                  </Label>
                  <Input
                    id="location"
                    name="location"
                    placeholder="City, State"
                    value={profileData.location || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2 md:col-span-2">
                  <Label htmlFor="website" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                    <Globe className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Website
                  </Label>
                  <Input
                    id="website"
                    name="website"
                    placeholder="https://yourwebsite.com"
                    value={profileData.website || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                About You
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Tell us more about yourself</CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="bio" className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700">
                  <Info className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                  Bio
                </Label>
                <Textarea
                  id="bio"
                  name="bio"
                  placeholder="Add a short bio about yourself..."
                  className={`min-h-[100px] sm:min-h-[120px] text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  value={profileData.bio || ""}
                  onChange={handleChange}
                />
              </div>
            </CardContent>
          </Card>

          <div className="mt-6 sm:mt-8 border-t pt-4 sm:pt-6">
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="pb-2 px-4 sm:px-6">
                <CardTitle className="text-base sm:text-lg flex items-center gap-2 text-red-700">
                  <Trash2 className="h-4 w-4 sm:h-5 sm:w-5 text-red-600" />
                  Delete Account
                </CardTitle>
                <CardDescription className="text-xs sm:text-sm text-red-600">
                  Permanently delete your account and all associated data
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-2 pb-4 px-4 sm:px-6">
                <p className="text-xs sm:text-sm text-red-700 mb-4">
                  This action cannot be undone. Once you delete your account, all of your data including profile
                  information, resumes, and application history will be permanently removed.
                </p>
                <Button
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700 text-xs sm:text-sm py-1.5 px-3 sm:py-2 sm:px-4"
                  onClick={() => {
                    if (
                      window.confirm(
                        "Are you sure you want to delete your account? This action cannot be undone."
                      )
                    ) {
                      handleDeleteAccount()
                    }
                  }}
                >
                  <Trash2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1.5 sm:mr-2" />
                  Delete Account
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="education" className="space-y-4">
          {/* Education Details Card */}
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <GraduationCap className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Education Details
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Your educational background and current study details
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <div className="grid gap-4 sm:gap-5 grid-cols-1 md:grid-cols-2">
                <div className="space-y-1.5 sm:space-y-2">
                  <Label
                    htmlFor="education"
                    className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700"
                  >
                    <GraduationCap className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Education Level
                  </Label>
                  <Input
                    id="education"
                    name="education"
                    placeholder="Enter your education level"
                    value={profileData.education}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label
                    htmlFor="current_school"
                    className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700"
                  >
                    <Building className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    Current School
                  </Label>
                  <Input
                    id="current_school"
                    name="current_school"
                    placeholder="Enter your current school"
                    value={profileData.current_school || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label
                    htmlFor="school_attended"
                    className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700"
                  >
                    School Attended
                  </Label>
                  <Input
                    id="school_attended"
                    name="school_attended"
                    placeholder="Enter your previous school"
                    value={profileData.school_attended || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label
                    htmlFor="sat_act_score"
                    className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700"
                  >
                    <Award className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    SAT/ACT Score
                  </Label>
                  <Input
                    id="sat_act_score"
                    name="sat_act_score"
                    placeholder="Enter your SAT/ACT score"
                    value={profileData.sat_act_score || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
                <div className="space-y-1.5 sm:space-y-2">
                  <Label
                    htmlFor="mcat_score"
                    className="flex items-center gap-1.5 text-xs sm:text-sm font-light text-gray-700"
                  >
                    <Award className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-blue-500" />
                    MCAT Score
                  </Label>
                  <Input
                    id="mcat_score"
                    name="mcat_score"
                    placeholder="Enter your MCAT score"
                    value={profileData.mcat_score || ""}
                    onChange={handleChange}
                    className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Experience Card */}
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <Building className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Work Experience
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Detail your work experience, internships, or relevant roles
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <Textarea
                id="experience"
                name="experience"
                placeholder="Describe your work experience..."
                className={`min-h-[100px] sm:min-h-[120px] text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                value={profileData.experience || ""}
                onChange={handleChange}
              />
            </CardContent>
          </Card>

          {/* Projects Card */}
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <BookOpen className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Projects
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Showcase your key projects and contributions
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <Textarea
                id="projects"
                name="projects"
                placeholder="Describe your projects, portfolio, or research works..."
                className={`min-h-[100px] sm:min-h-[120px] text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
                value={profileData.projects || ""}
                onChange={handleChange}
              />
            </CardContent>
          </Card>

          {/* Skills & Interests Card */}
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Skills & Interests
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                List your key skills, areas of expertise, or interests
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <Input
                id="skills"
                name="skills"
                placeholder="E.g. JavaScript, Python, Design, Marketing"
                value={profileData.skills || ""}
                onChange={handleChange}
                className={`text-sm ${isEditing ? "border-blue-200 focus:border-blue-400" : "bg-gray-50"}`}
              />
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="resumes" className="space-y-4">
          <Card>
            <CardHeader className="pb-2 px-4 sm:px-6">
              <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                Resume Management
              </CardTitle>
              <CardDescription className="text-xs sm:text-sm">Upload and manage your resumes for job applications</CardDescription>
            </CardHeader>
            <CardContent className="pt-2 pb-4 px-4 sm:px-6">
              <div className="space-y-4 sm:space-y-6">
                <div className="bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-100 flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                  <div className="flex-1">
                    <h3 className="font-light text-blue-800 text-sm sm:text-base mb-1">Upload New Resume</h3>
                    <p className="text-xs sm:text-sm text-blue-600">
                      Upload your resume in PDF, DOC, or DOCX format
                    </p>
                  </div>
                  <div>
                    <Label
                      htmlFor="resume-upload"
                      className="cursor-pointer inline-flex items-center gap-1.5 sm:gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-md transition-colors text-xs sm:text-sm"
                    >
                      <Upload className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                      Upload Resume
                    </Label>
                    <Input
                      id="resume-upload"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={handleResumeUpload}
                      className="hidden"
                    />
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-base sm:text-lg font-light text-gray-800 mb-2 sm:mb-3 flex items-center gap-1.5 sm:gap-2">
                    <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                    Your Resumes
                  </h3>

                  {resumes && resumes.length > 0 ? (
                    <div className="space-y-2 sm:space-y-3">
                      {resumes.map((resume) => (
                        <div
                          key={resume.url}
                          className={`p-3 sm:p-4 rounded-lg border ${
                            profileData.default_resume_url === resume.url
                              ? "bg-blue-50 border-blue-200"
                              : "bg-gray-50 border-gray-200"
                          }`}
                        >
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-1.5 sm:gap-2">
                                <FileText
                                  className={`h-4 w-4 sm:h-5 sm:w-5 ${
                                    profileData.default_resume_url === resume.url ? "text-blue-600" : "text-gray-600"
                                  }`}
                                />
                                <h4 className="font-light text-sm sm:text-base truncate max-w-[150px] sm:max-w-[200px]">{resume.name}</h4>
                                {profileData.default_resume_url === resume.url && (
                                  <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs">Default</Badge>
                                )}
                              </div>
                              <p className="text-xs text-gray-500 mt-1 flex items-center gap-1.5">
                                <Calendar className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                                Uploaded: {new Date(resume.uploaded_at).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="flex flex-wrap items-center gap-2 mt-2 sm:mt-0">
                              {profileData.default_resume_url !== resume.url && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSetDefaultResume(resume.url)}
                                  className="text-xs border-blue-300 text-blue-700 hover:bg-blue-50 h-7 sm:h-8 px-2 py-0"
                                >
                                  <Check className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                                  Set Default
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(resume.url, "_blank")}
                                className="text-xs h-7 sm:h-8 px-2 py-0"
                              >
                                <Download className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRemoveResume(resume.url)}
                                className="text-xs border-red-300 text-red-700 hover:bg-red-50 h-7 sm:h-8 px-2 py-0"
                              >
                                <Trash2 className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1" />
                                Remove
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 sm:py-10 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                      <FileText className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2 sm:mb-3" />
                      <h4 className="text-base sm:text-lg font-light text-gray-600 mb-1">No Resumes Uploaded</h4>
                      <p className="text-xs sm:text-sm text-gray-500">
                        Upload your first resume to get started with job applications
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}