"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Portal } from "@/components/ui/portal";

export interface Step {
  targetId: string;
  title: string;
  message: string;
  placement?: "top" | "bottom" | "left" | "right";
  // New property to signal which tab should be active when this step is visible.
  tab?: "profile" | "bookmarks" | "job-applicants" | "outreach" | "outreach-stats" | "personal" | "education" | "resumes";
}

export interface OnboardingPopupProps {
  onComplete: () => void;
  // Optional callback that fires on step change.
  onStepChange?: (currentStep: number, step: Step) => void;
  steps?: Step[];
}

const defaultSteps: Step[] = [
  {
    targetId: "profile-header",
    title: "Welcome to Your Dashboard",
    message:
      "This is your profile dashboard where you can manage all your information and activities.",
    placement: "bottom",
    tab: "profile", // Stays on the profile view
  },
  {
    targetId: "profile-nav",
    title: "Navigation",
    message:
      "Use this sidebar to navigate between different sections of your dashboard.",
    placement: "right",
    tab: "profile",
  },
  // New inner tab steps: when the user is on the Profile view, walk them through inner tabs.
  {
    targetId: "personal-tab",
    title: "Your Personal Information",
    message: "This tab shows your basic details and contact information.",
    placement: "bottom",
    tab: "personal",
  },
  {
    targetId: "profile-content",
    title: "Profile Management",
    message: "Here you can update your personal information, education details, and upload your resumes.",
    placement: "left",
    tab: "profile",
  },
  {
    targetId: "education-tab",
    title: "Education & Experience",
    message: "Switch to this tab to update your educational background and work experience.",
    placement: "bottom",
    tab: "education",
  },
  {
    targetId: "resumes-tab",
    title: "Resume Management",
    message: "Visit this tab to upload, view, or remove your resumes.",
    placement: "bottom",
    tab: "resumes",
  },
  // You can keep your existing steps or adjust them as needed:
  {
    targetId: "bookmarks-section",
    title: "Bookmarks",
    message: "Access all your saved opportunities in the Bookmarks section.",
    placement: "bottom",
    tab: "bookmarks",
  },
  {
    targetId: "job-applicants-section",
    title: "Job Applications",
    message: "Review applications from users who apply to your approved job postings.",
    placement: "bottom",
    tab: "job-applicants",
  },
  {
    targetId: "tokens-display",
    title: "Tokens",
    message: "These tokens can be used to unlock premium listings on the Outreach page.",
    placement: "right",
    tab: "profile",
  },
  
];

export default function OnboardingPopup({
  onComplete,
  onStepChange,
  steps = defaultSteps,
}: OnboardingPopupProps) {
  // ... (rest of the implementation remains the same as provided)
  // The implementation uses state, ref, effects for tooltips, etc.
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });
  const tooltipRef = useRef<HTMLDivElement>(null);

  // ... [Same useEffects, handleNext, handleSkip, and positioning logic]
  
  // Call onStepChange callback when currentStep changes.
  useEffect(() => {
    if (onStepChange) {
      onStepChange(currentStep, steps[currentStep]);
    }
  }, [currentStep, steps, onStepChange]);

  // Update target element position when step changes or window resizes.
  useEffect(() => {
    const updateTargetRect = () => {
      const currentStepInfo = steps[currentStep];
      const targetEl = document.getElementById(currentStepInfo.targetId);
      
      if (targetEl) {
        const rect = targetEl.getBoundingClientRect();
        setTargetRect(rect);
        
        // Scroll the target element into view with a smooth animation
        targetEl.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    };

    updateTargetRect();
    
    const timer = setTimeout(updateTargetRect, 300);
    return () => clearTimeout(timer);
  }, [currentStep, steps, windowSize]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      localStorage.setItem("onboardingComplete", "true");
      onComplete();
    }
  };

  const handleSkip = () => {
    localStorage.setItem("onboardingComplete", "true");
    onComplete();
  };

  const getTooltipPosition = () => {
    if (!targetRect) return { top: 0, left: 0 };
    
    const currentStepInfo = steps[currentStep];
    const placement = currentStepInfo.placement || "bottom";
    const tooltipWidth = tooltipRef.current?.offsetWidth || 300;
    const tooltipHeight = tooltipRef.current?.offsetHeight || 120;
    const margin = 12;
    
    let top = 0;
    let left = 0;
    
    switch (placement) {
      case "top":
        top = targetRect.top - tooltipHeight - margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
        break;
      case "bottom":
        top = targetRect.bottom + margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
        break;
      case "left":
        top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
        left = targetRect.left - tooltipWidth - margin;
        break;
      case "right":
        top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
        left = targetRect.right + margin;
        break;
    }
    
    if (left < 16) left = 16;
    if (left + tooltipWidth > windowSize.width - 16) {
      left = windowSize.width - tooltipWidth - 16;
    }
    if (top < 16) top = 16;
    if (top + tooltipHeight > windowSize.height - 16) {
      top = windowSize.height - tooltipHeight - 16;
    }
    
    return { top, left };
  };

  const tooltipPosition = getTooltipPosition();

  if (!targetRect) return null;

  return (
    <Portal>
      <div
        className="fixed inset-0 bg-black/50 z-[999]"
        onClick={handleSkip}
        aria-hidden="true"
      />
      <div
        className="fixed pointer-events-none z-[1000] transition-all duration-300 ease-in-out"
        style={{
          top: targetRect.top - 4,
          left: targetRect.left - 4,
          width: targetRect.width + 8,
          height: targetRect.height + 8,
          boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 15px rgba(0, 112, 243, 0.7)",
          borderRadius: "4px",
          border: "2px solid #0070f3",
        }}
      />
      <div
        ref={tooltipRef}
        className="fixed z-[1001] bg-white rounded-lg shadow-xl p-4 w-[300px] max-w-[calc(100vw-32px)] transition-all duration-300 ease-in-out"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium text-gray-900">{steps[currentStep].title}</h3>
          <div className="text-sm text-gray-500">
            {currentStep + 1}/{steps.length}
          </div>
        </div>
        <p className="text-gray-600 mb-4">{steps[currentStep].message}</p>
        <div className="flex justify-between items-center">
          <Button variant="ghost" size="sm" onClick={handleSkip}>
            Skip tour
          </Button>
          <Button onClick={handleNext}>
            {currentStep < steps.length - 1 ? "Next" : "Finish"}
          </Button>
        </div>
      </div>
    </Portal>
  );
}
