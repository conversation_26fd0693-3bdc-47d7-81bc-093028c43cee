'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Navbar from '@/components/Navbar';
import AdNavbar from '@/components/NavbarAd';
import Footer from '@/components/Footer';
import { Stethoscope, GraduationCap, BookOpen, Users, ArrowRight, Search, Briefcase, Star, Phone, CircleUser } from 'lucide-react';
import Hero from '@/components/Hero';
import ScrollingCards from '@/components/ScrollingCards';
import { motion } from 'framer-motion';

type Testimonial = {
  quote: string;
  author: string;
  role: string;
  stars: number;
};

const testimonials: Testimonial[] = [
  {
    quote: "<PERSON>linn is intuitive to use - I only had to click once to find a useful clinic",
    author: "<PERSON>",
    role: "Co-Founder @ Runway Mobile",
    stars: 5
  },
  {
    quote: "Amazing UI, super helpful for anyone trying to get into medicine. Clean and intuitive, great design. I could find any clinic I want on here.",
    author: "<PERSON>",
    role: "Co-Founder @ <PERSON>way Mobile",
    stars: 5
  },
  {
    quote: "Klinn is a website that goes above and beyond for the user: I found multiple opportunities that I actually pursued from a database of hundreds spanning almost every field of study imaginable! If you're an ambitious high school or college student Klinn is indispensable and always puts its users first!",
    author: "Allen D",
    role: "High School Student, Class of 2027",
    stars: 5
  }
];

const logos = [
  { src: "/images/lander/usc.png", alt: "USC"},
  { src: "/images/lander/emory.png", alt: "Emory University"},
  { src: "/images/lander/gt.png", alt: "Georgia Tech"},
  { src: "/images/lander/uiuc.png", alt: "University of Illinois"},
  { src: "/images/lander/vand.png", alt: "Vanderbilt University"},
  { src: "/images/lander/umd.png", alt: "University of Maryland"},
  { src: "/images/lander/sfsu.png", alt: "San Francisco State University"},
  { src: "/images/lander/berkeley.png", alt: "UC Berkeley"},
  { src: "/images/lander/cmu.png", alt: "Carnegie Mellon University"},
];

const carddata = [
  { title: "Student-Led", description: "Gain insights from those who've recently succeeded" },
  { title: "Comprehensive Support", description: "From clinical experience to admissions guidance" },
  { title: "Tailored Approach", description: "Personalized advice for your unique journey" },
  { title: "Diverse Expertise", description: "Consultants from various majors, not just Pre-Meds" }
];

const fadeInUpVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerChildren = {
  visible: {
    transition: {
      staggerChildren: 0.2
    }
  }
};

interface FeatureCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  delay?: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon: Icon, title, description, buttonText, buttonLink, delay = 0 }) => (
  <motion.div
    initial="hidden"
    whileInView="visible"
    viewport={{ once: true }}
    variants={fadeInUpVariants}
    transition={{ duration: 0.6, delay }}
  >
    <Card className="group h-full transition-all duration-300 hover:shadow-xl bg-white/90 backdrop-blur-sm">
      <CardHeader>
        <motion.div
          className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4"
          whileHover={{ scale: 1.1, rotate: 360 }}
          transition={{ duration: 0.6 }}
        >
          <Icon className="w-6 h-6 text-blue-600" />
        </motion.div>
        <CardTitle className="text-xl mb-2 text-blue-900">{title}</CardTitle>
        <CardDescription className="text-blue-700">{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Link href={buttonLink} passHref>
          <Button 
            variant="outline" 
            className="w-full group text-blue-900 border-blue-900 hover:bg-blue-900 hover:text-white"
          >
            {buttonText}
            <motion.span
              className="ml-2"
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            >
              <ArrowRight className="h-4 w-4" />
            </motion.span>
          </Button>
        </Link>
      </CardContent>
    </Card>
  </motion.div>
);

const HomePage = () => {
  useEffect(() => {
    document.title = 'Klinn | Home';
  }, []); 

  const [hoveredLogo, setHoveredLogo] = useState<number | null>(null);

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-r from-blue-500 via-blue-400 to-blue-500">
      <Navbar/>
      <Hero />
      <main className="flex-grow">
      <section className="py-24 bg-blue-200">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-2 max-w-6xl mx-auto relative">
              {/* Separator */}
              <div className="absolute hidden md:block h-3/4 w-px bg-white/80 left-1/2 top-1/2 transform -translate-y-1/2"></div>
              
              {/* What is Klinn? */}
              <motion.div 
                className="flex flex-col md:pr-20"
                variants={fadeInUpVariants}
              >
                <h2 className="text-2xl font-light text-blue-900 mb-8">What is Klinn?</h2>
                <p className="text-blue-800/90 leading-relaxed">
                  Klinn is a student-led EdTech startup making the healthcare field more accessible for aspiring students. Whether you&apos;re an undergraduate in a pre-medical or BS/MD program or even a high schooler interested in medicine or any other field, Klinn is here for you!
                </p>
              </motion.div>
              
              {/* How Klinn Empowers */}
              <motion.div
                className="flex flex-col md:pl-20 mt-12 md:mt-0"
                variants={fadeInUpVariants}
                transition={{ delay: 0.1 }}
              >
                <h2 className="text-2xl font-light text-blue-900 mb-8">How Klinn Empowers Your Success</h2>
                
                <div className="space-y-6">
                  
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 text-blue-700">
                      <BookOpen className="w-5 h-5" strokeWidth={1.5} />
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-blue-800 mb-1">Medical Extracurriculars</h3>
                      <p className="text-blue-800/80 text-sm">Discover and engage in activities that enhance your application at all education levels.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="mr-4 mt-1 text-blue-700">
                      <CircleUser className="w-5 h-5" strokeWidth={1.5} />
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-blue-800 mb-1">Job Board</h3>
                      <p className="text-blue-800/80 text-sm">Search through opportunities posted directly by clinics, professors, and startups. Upload your resume and apply to opportunities with the click of a button.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="mr-4 mt-1 text-blue-700">
                      <Phone className="w-5 h-5" strokeWidth={1.5} />
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-blue-800 mb-1">Cold Outreach Dashboard</h3>
                      <p className="text-blue-800/80 text-sm">Automate your internship cold outreach. Search through our database of 11,000+ clinics, 1500+ professors, 1000+ startups, and get matched to the perfect fit.</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <Link href="/auth" passHref>
                    <Button 
                      className="bg-transparent hover:bg-blue-900 text-blue-900 hover:text-white border border-blue-900 px-6 py-2 text-xs uppercase tracking-wider transition-colors duration-300 rounded"
                    >
                      Get Started
                    </Button>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        <ScrollingCards />

        {/* <motion.section 
          className="py-20 bg-blue-200 text-blue-900"
          variants={staggerChildren}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-3xl font-semibold mb-12 text-center"
              variants={fadeInUpVariants}
            >
              What Our Users Say
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUpVariants}
                  transition={{ delay: index * 0.2 }}
                >
                  <Card className="bg-white shadow-lg overflow-hidden h-full transform transition-all duration-300 hover:scale-105">
                    <CardContent className="p-8">
                      <motion.div 
                        className="flex items-center mb-6"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 0.5 + index * 0.1 }}
                      >
                        {[...Array(testimonial.stars)].map((_, i) => (
                          <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                        ))}
                      </motion.div>
                      <p className="text-xl italic mb-6">&quot;{testimonial.quote}&quot;</p>
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                          <span className="text-blue-600 font-semibold text-xl">
                            {testimonial.author.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-semibold text-lg">{testimonial.author}</p>
                          <p className="text-blue-700">{testimonial.role}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section> */}

        <motion.section 
          className="py-16 bg-blue-200 text-blue-900"
          variants={staggerChildren}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="container mx-auto px-4 max-w-5xl">
            <motion.h2 
              className="text-2xl font-light mb-10 text-center"
              variants={fadeInUpVariants}
            >
              What Our Users Say
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  variants={fadeInUpVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="bg-white border border-gray-100 rounded-lg h-full transition-all duration-200 hover:shadow-md flex flex-col">
                    <div className="p-6 flex-grow">
                      <div className="flex mb-4">
                        {[...Array(testimonial.stars)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-blue-500 fill-current" />
                        ))}
                      </div>
                      <p className="text-base">&quot;{testimonial.quote}&quot;</p>
                    </div>
                    <div className="mt-auto border-t border-gray-100 p-4">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-3">
                          <span className="text-blue-500 font-medium text-sm">
                            {testimonial.author.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{testimonial.author}</p>
                          <p className="text-gray-500 text-xs">{testimonial.role}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>

        <section className="py-16 bg-blue-200">
          <div className="container mx-auto px-4 max-w-5xl">
            <motion.h2 
              className="text-2xl font-light mb-12 text-center text-blue-900"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Crafted By a Team With Acceptances From
            </motion.h2>
            <div className="flex flex-wrap justify-center items-center gap-2 md:gap-12">
              {logos.map((logo, index) => (
                <motion.div
                  key={index}
                  className="relative w-16 md:w-32 h-16 md:h-32 flex items-center justify-center overflow-hidden"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.05 }}
                  onMouseEnter={() => setHoveredLogo(index)}
                  onMouseLeave={() => setHoveredLogo(null)}
                >
                  <Image 
                    src={logo.src} 
                    alt={logo.alt} 
                    width={120}
                    height={120}
                    style={{ 
                      objectFit: 'contain',
                      opacity: 0.8,
                      position: 'relative',
                      zIndex: 1
                    }}
                    className="transition-all duration-200 hover:opacity-100"
                  />
                  {hoveredLogo === index && (
                    <motion.div 
                      className="z-10 absolute inset-0 bg-blue-400 bg-opacity-80 flex items-center justify-center rounded-lg"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      <p className="z-10 text-sm text-white font-medium px-2 text-center">{logo.alt}</p>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>


        <section className="bg-gradient-to-b from-blue-200 to-blue-300 py-24">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* First CTA Block */}
            <div className="flex flex-col justify-center border-b md:border-b-0 md:border-r border-gray-100 pb-12 md:pb-0 md:pr-12">
              <h2 className="text-2xl font-light text-blue-900 mb-6">Launch Your Healthcare Career</h2>
              <p className="text-blue-700/60 mb-8 text-sm">
                Access the resources and guidance you need to succeed with Klinn.
              </p>
              <div className="mt-auto">
                <Link href="/auth" passHref>
                  <Button 
                    className="bg-transparent hover:bg-blue-900 text-blue-900 hover:text-white border border-blue-900 px-6 py-2 text-xs uppercase tracking-wider transition-colors duration-300 rounded-md"
                  >
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
            
            {/* Second CTA Block */}
            <div className="flex flex-col justify-center md:pl-12">
              <h2 className="text-2xl font-light text-blue-900 mb-6">Join Our Mission</h2>
              <p className="text-blue-700/60 mb-8 text-sm">
                For passionate individuals looking to transform healthcare education.
              </p>
              <div className="mt-auto">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center group text-blue-900 hover:text-blue-800"
                >
                  <span className="text-xs uppercase tracking-wider border-b border-transparent group-hover:border-blue-800 pb-1 transition-all duration-300">
                    <EMAIL>
                  </span>
                  <svg className="ml-2 h-3 w-3 transform group-hover:translate-x-1 transition-transform duration-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      </main>

      <Footer className="bg-gradient-to-b from-blue-300 to-blue-500 text-white p-8 transition-all duration-300 ease-in-out" />
    </div>
  );
};

export default HomePage;

