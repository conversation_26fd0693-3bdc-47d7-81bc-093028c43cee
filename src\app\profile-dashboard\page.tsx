"use client";

import { useState, useEffect, type ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "../../../supabase/supabaseClient";
import { useAuth } from "../../context/AuthContext";

// UI components
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

// Icons
import {
  Bookmark,
  Briefcase,
  ChevronRight,
  GraduationCap,
  Mail,
  MapPin,
  Menu,
  User,
  FileText,
  Award,
  Calendar,
  Shield,
  BookOpen,
  Sparkles,
  Bell,
  X,
} from "lucide-react";

// Other components
import Navbar from "@/components/Navbar";
import { AdminView } from "@/components/AdminView";
import { TokenDisplay } from "@/components/TokenDisplay";

// Content components
import ProfileContent from "../../components/profile-content";
import BookmarksContent from "../../components/bookmarks-content";
import JobApplicantsContent from "../../components/job-applicants-content";
import Footer from "@/components/Footer";
import OnboardingPopup, { Step } from "@/components/OnboardingPopup";
import ReferralLink from "@/components/ReferralLink";

// ----- TYPES -----
interface ProfileData {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  education: string;
  current_school?: string;
  location?: string;
  sat_act_score?: string;
  mcat_score?: string;
  bio?: string;
  website?: string;
  school_attended?: string;
  schools_accepted?: string;
  experience?: string;
  projects?: string;
  skills?: string;
  resumes?: Array<{
    name: string;
    url: string;
    uploaded_at: string;
  }>;
  default_resume_url?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  tokens: number;
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  compensation: string;
  postedAt: number;
  customQuestions?: string[];
}

interface Applicant {
  id: string;
  name: string;
  resumeUrl: string;
  appliedAt: number;
  customAnswers?: string[];
}

// ----- GOOGLE SHEETS FETCHING FUNCTIONS -----
async function fetchAllOpportunities(): Promise<any[]> {
  try {
    const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_SHEETS_API_KEY;
    const SPREADSHEET_ID = process.env.NEXT_PUBLIC_SPREADSHEET_ID_2;
    const SHEET_NAME = "Sheet1";
    const RANGE = "A1:Z105";
    const url = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/${SHEET_NAME}!${RANGE}?key=${API_KEY}`;
    const response = await fetch(url);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    const result = await response.json();
    if (!result.values || result.values.length <= 1) return [];
    const headers = result.values[0];
    const rows = result.values.slice(1);
    const mapped = rows.map((row: string[], index: number) => {
      const mappedData = headers.reduce((acc: Record<string, string>, header: string, i: number) => {
        acc[header] = row[i] || "";
        return acc;
      }, {});
      return {
        id: `op-${index}`,
        title: mappedData["Name"],
        description: mappedData["Description"],
        opportunityType: mappedData["Opportunity Type"],
        location: mappedData["Location"] ? mappedData["Location"].split(",").map((loc: string) => loc.trim()) : [],
        age: mappedData["Ages"] || "",
        grades: mappedData["Grades"] || "",
        submissionDate: mappedData["Opportunity Date"] || "",
        compensation: mappedData["Compensation"] || "",
        fee: mappedData["Fee ($)"] || "",
        competitiveness: mappedData["Prestige/Competitiveness"] || "",
        website: mappedData["Website"] || "",
        contact: mappedData["Contact"] || "",
        sponsor: mappedData["Sponsor"] || "",
      };
    });
    return mapped;
  } catch (err) {
    console.error("Error fetching from Google Sheets:", err);
    return [];
  }
}

async function fetchAllMedOpportunities(): Promise<any[]> {
  try {
    const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_SHEETS_API_KEY;
    const SPREADSHEET_ID = process.env.NEXT_PUBLIC_SPREADSHEET_ID;
    const SHEET_NAME = "Opportunities";
    const RANGE = "A1:Z105";
    const url = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/${SHEET_NAME}!${RANGE}?key=${API_KEY}`;
    const response = await fetch(url);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    const result = await response.json();
    if (!result.values || result.values.length <= 1) return [];
    const headers = result.values[0];
    const rows = result.values.slice(1);
    const mappedMedOpps = rows.map((row: string[]) => {
      const rowData: Record<string, string> = {};
      headers.forEach((header: string, i: number) => {
        rowData[header] = row[i] || "";
      });
      return {
        opportunity: {
          Name: rowData["Name"] || "",
          Location: rowData["Location"] || "",
          Ages: rowData["Ages"] || "",
          Grades: rowData["Grades"] || "",
          Website: rowData["Website"] || "",
          "Opportunity Date": rowData["Opportunity Date"] || "",
          "Opportunity Type": rowData["Opportunity Type"] || "",
          "Fee ($)": rowData["Fee ($)"] || "",
          Compensation: rowData["Compensation"] || "",
          "Prestige/Competitiveness": rowData["Prestige/Competitiveness"] || "",
          Contact: rowData["Contact"] || "",
          Description: rowData["Description"] || "",
        },
      };
    });
    return mappedMedOpps;
  } catch (err) {
    console.error("Error fetching MED opportunities:", err);
    return [];
  }
}

export default function Profile() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  // State for profile and UI data
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [resumes, setResumes] = useState<ProfileData["resumes"]>([]);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<"profile" | "bookmarks" | "job-applicants" | "outreach" | "outreach-stats">("profile");
  const [innerProfileTab, setInnerProfileTab] = useState("personal");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [bookmarkedOpportunities, setBookmarkedOpportunities] = useState<Array<any>>([]);
  const [isLoadingBookmarks, setIsLoadingBookmarks] = useState(false);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [jobApplicantsMap, setJobApplicantsMap] = useState<Record<string, Applicant[]>>({});
  const [jobPostingsLoading, setJobPostingsLoading] = useState<boolean>(false);
  const [googleToken, setGoogleToken] = useState<string | null>(null);
  const [saveConfirmation, setSaveConfirmation] = useState<"idle" | "saving" | "success">("idle");
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Show onboarding after a brief delay to ensure the page is loaded.
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!localStorage.getItem("onboardingComplete")) {
        setShowOnboarding(true);
      }
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!user) return;          // guard until user is ready
  
    const channel = supabase
      .channel(`profile_changes:${user.id}`)  // unique topic
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',      // we only care about updates
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${user.id}`,
        },
        payload => {
          if (payload.new) setProfileData(payload.new as ProfileData);
        }
      )
      .subscribe(status => {
        if (status === 'SUBSCRIBED')   console.log('Realtime profile subscribed');
        if (status === 'CHANNEL_ERROR') console.error('Realtime profile error');
      });
  
    // cleanup on unmount or when user.id changes
    return () => {
      supabase.removeChannel(channel);
      console.log('Realtime profile unsubscribed');
    };
  }, [user?.id]);               // ← runs exactly once per signed-in user
  

  useEffect(() => {
    async function getSession() {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.provider_token || null;
      setGoogleToken(token);

      if (token) {
        sessionStorage.setItem("google_token", token);
      }
    }
    getSession();
  }, []);

  useEffect(() => {
    if (authLoading) return;
    if (!user) {
      router.push("/auth");
      return;
    }
    const initData = async () => {
      try {
        await Promise.all([fetchProfile(), fetchJobPostingsAndApplicants()]);
      } catch (error) {
        console.error("Error during initial fetch:", error);
      }
    };
    initData();
  }, [authLoading, user, router]);

  useEffect(() => {
    const updateTokensFromArchive = async () => {
      if (!profileData) return;
      const userEmail = profileData.email.toLowerCase();
      const { data: archivedData, error: archivedError } = await supabase
        .from("archived_users")
        .select("tokens")
        .eq("email", userEmail)
        .maybeSingle();

      if (archivedError) {
        console.error("Error checking archived record:", archivedError);
        return;
      }

      if (archivedData && archivedData.tokens !== undefined && archivedData.tokens !== profileData.tokens) {
        const { error: updateError } = await supabase
          .from("profiles")
          .update({ tokens: archivedData.tokens })
          .eq("id", profileData.id);
        if (updateError) {
          console.error("Error updating tokens from archive:", updateError);
          return;
        }

        await supabase.from("archived_users").delete().eq("email", userEmail);
        setProfileData((prev: any) => ({ ...prev, tokens: archivedData.tokens }));
      }
    };

    updateTokensFromArchive();
  }, [profileData]);

  const fetchProfile = async () => {
    if (!user) return;

    const { data, error } = await supabase.from("profiles").select("*").eq("id", user.id).maybeSingle();

    if (error) {
      console.error("Error fetching profile:", error);
      return;
    }

    if (!data) {
      try {
        const { error: insertError } = await supabase.from("profiles").upsert({
          id: user.id,
          email: user.email,
          first_name: "",
          last_name: "",
          education: "",
          location: "",
          sat_act_score: "",
          mcat_score: "",
          bio: "",
          website: "",
          school_attended: "",
          schools_accepted: "",
          experience: "",
          projects: "",
          skills: "",
          resumes: null,
          default_resume_url: null,
          created_at: new Date(),
          updated_at: new Date(),
          role: "general",
          tokens: 5,
        });

        if (insertError) {
          console.error("Error auto-creating profile row:", insertError);
          return;
        }

        const { data: newProfile } = await supabase.from("profiles").select("*").eq("id", user.id).maybeSingle();
        if (newProfile) {
          setProfileData(newProfile);
          setResumes(newProfile.resumes || []);
        }
      } catch (err) {
        console.error("Unexpected error creating new profile:", err);
      }
    } else {
      setProfileData(data);
      setResumes(data.resumes || []);
    }
  };

  // Define a helper type guard to check for inner tabs.
function isInnerTab(tab: Step["tab"]): tab is "personal" | "education" | "resumes" {
  return tab === "personal" || tab === "education" || tab === "resumes";
}


  // Modify the onboarding step change handler:
  const handleOnboardingStepChange = (currentStep: number, step: Step) => {
    if (step.tab) {
      if (isInnerTab(step.tab)) {
        // When the step relates to an inner tab:
        setViewMode("profile"); // Force the outer view to Profile
        setInnerProfileTab(step.tab);
      } else {
        // Here, step.tab must be one of the outer view types.
        setViewMode(step.tab);
      }
    }
  };
  
  

  const sanitizeInput = (input: string): string => {
    // Remove any HTML tags and special characters
    return input.replace(/[<>]/g, '').trim();
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateWebsite = (url: string): boolean => {
    if (!url) return true; // Empty URL is valid
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const saveProfileData = async () => {
    if (!user || !profileData) return;

    setSaveConfirmation("saving");

    try {
      // Sanitize and validate inputs
      const sanitizedData = {
        ...profileData,
        first_name: sanitizeInput(profileData.first_name),
        last_name: sanitizeInput(profileData.last_name),
        email: profileData.email, // Email shouldn't be changed
        education: sanitizeInput(profileData.education),
        current_school: profileData.current_school ? sanitizeInput(profileData.current_school) : undefined,
        location: profileData.location ? sanitizeInput(profileData.location) : undefined,
        sat_act_score: profileData.sat_act_score ? sanitizeInput(profileData.sat_act_score) : undefined,
        mcat_score: profileData.mcat_score ? sanitizeInput(profileData.mcat_score) : undefined,
        bio: profileData.bio ? sanitizeInput(profileData.bio) : undefined,
        website: profileData.website ? sanitizeInput(profileData.website) : undefined,
        school_attended: profileData.school_attended ? sanitizeInput(profileData.school_attended) : undefined,
        experience: profileData.experience ? sanitizeInput(profileData.experience) : undefined,
        projects: profileData.projects ? sanitizeInput(profileData.projects) : undefined,
        skills: profileData.skills ? sanitizeInput(profileData.skills) : undefined,
        updated_at: new Date().toISOString(),
      };

      // Validate critical fields
      if (!sanitizedData.first_name || !sanitizedData.last_name) {
        throw new Error("First name and last name are required");
      }

      if (!validateEmail(sanitizedData.email)) {
        throw new Error("Invalid email format");
      }

      if (sanitizedData.website && !validateWebsite(sanitizedData.website)) {
        throw new Error("Invalid website URL");
      }

      const { error } = await supabase
        .from("profiles")
        .update(sanitizedData)
        .eq("id", user.id);

      if (error) {
        console.error("Error saving profile:", error);
        throw error;
      }

      setSaveConfirmation("success");
      setIsEditing(false);
      await fetchProfile();
      setTimeout(() => {
        setSaveConfirmation("idle");
      }, 2000);
    } catch (err) {
      console.error("Error saving profile:", err);
      setSaveConfirmation("idle");
      alert(err instanceof Error ? err.message : "Failed to save profile. Please try again.");
    }
  };

  const handleResumeUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // File validation
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    const ALLOWED_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    try {
      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        throw new Error(`File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
      }

      // Validate file type
      if (!ALLOWED_TYPES.includes(file.type)) {
        throw new Error('Only PDF and Word documents are allowed');
      }

      // Sanitize filename
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const uniqueFileName = `${Date.now()}-${sanitizedFileName}`;
      const filePath = `${user.id}/${uniqueFileName}`;

      // Upload with content type validation
      const { error: uploadError } = await supabase.storage
        .from("resumes")
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: file.type
        });

      if (uploadError) {
        console.error("Upload error details:", uploadError);
        throw uploadError;
      }

      // Get a signed URL instead of public URL for better security
      const { data: signedUrlData, error: signedUrlError } = await supabase.storage
        .from("resumes")
        .createSignedUrl(filePath, 3600); // URL expires in 1 hour

      if (signedUrlError || !signedUrlData?.signedUrl) {
        throw new Error("Failed to generate secure URL");
      }

      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          resumes: [
            ...(profileData?.resumes || []),
            {
              name: sanitizedFileName,
              url: signedUrlData.signedUrl,
              uploaded_at: new Date().toISOString(),
            },
          ],
        })
        .eq("id", user.id);

      if (updateError) {
        console.error("Update error details:", updateError);
        throw updateError;
      }

      await fetchProfile();
      alert("Resume uploaded successfully!");
    } catch (err) {
      console.error("Error uploading resume:", err);
      alert(err instanceof Error ? err.message : "Failed to upload resume. Please try again.");
    }
  };

  const handleRemoveResume = async (resumeUrl: string) => {
    if (!user || !profileData) return;
    try {
      // Extract the file path from the URL
      const parts = resumeUrl.split(`/${user.id}/`);
      if (parts.length < 2) throw new Error("Invalid resume URL format");
      const filePath = `${user.id}/${parts[1]}`;

      // Remove the file from storage
      const { error: removeError } = await supabase.storage.from("resumes").remove([filePath]);
      if (removeError) throw removeError;

      // Update the profile data to remove the resume from the resumes array
      const updatedResumes = profileData.resumes?.filter(resume => resume.url !== resumeUrl) || [];
      
      // If the removed resume was the default, clear the default resume
      const updatedDefaultResume = profileData.default_resume_url === resumeUrl ? undefined : profileData.default_resume_url;

      // Update the profile in the database
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          resumes: updatedResumes,
          default_resume_url: updatedDefaultResume,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      if (updateError) throw updateError;

      // Update local state
      setProfileData(prev => prev ? {
        ...prev,
        resumes: updatedResumes,
        default_resume_url: updatedDefaultResume,
      } : null);
      setResumes(updatedResumes);

      alert("Resume removed successfully!");
    } catch (err) {
      console.error("Error removing resume:", err);
      alert("Failed to remove resume. Please try again.");
    }
  };

  const handleSetDefaultResume = async (resumeUrl: string) => {
    if (!user) return;
    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          default_resume_url: resumeUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);
      if (error) throw error;
      alert("Default resume updated!");
      await fetchProfile();
    } catch (err) {
      console.error("Error setting default resume:", err);
    }
  };

  async function fetchBookmarksForProfile() {
    if (!user) return [];
    const { data: bookmarkRows, error } = await supabase
      .from("bookmarks")
      .select("opportunity_id")
      .eq("user_id", user.id);
    if (error) {
      console.error("Error fetching bookmarks for profile:", error);
      return [];
    }
    return bookmarkRows.map((row: any) => row.opportunity_id);
  }

  async function fetchBookmarkedOpportunities() {
    const bookmarkedIds = await fetchBookmarksForProfile();
    if (!bookmarkedIds || bookmarkedIds.length === 0) {
      setBookmarkedOpportunities([]);
      return;
    }
    try {
      setIsLoadingBookmarks(true);
      const [allNonMedOpps, allMedOpps] = await Promise.all([fetchAllOpportunities(), fetchAllMedOpportunities()]);
      const filteredNonMed = allNonMedOpps.filter((opp) => bookmarkedIds.includes(opp.id));
      const filteredMed = allMedOpps.filter((opp) => bookmarkedIds.includes(opp.opportunity.Name));
      setBookmarkedOpportunities([...filteredNonMed, ...filteredMed]);
    } catch (err) {
      console.error("Error fetching bookmarked opportunities:", err);
    } finally {
      setIsLoadingBookmarks(false);
    }
  }

  const fetchJobPostingsAndApplicants = async () => {
    if (!user) return;
    try {
      setJobPostingsLoading(true);
      const { data: postingsData, error: postingsError } = await supabase
        .from("clinic_openings")
        .select("*")
        .eq("user_id", user.id)
        .eq("status", "approved");
      if (postingsError) throw postingsError;

      const postings: JobPosting[] = postingsData.map((row: any) => ({
        id: row.id,
        title: row.position_title,
        description: row.description,
        compensation: row.compensation,
        postedAt: new Date(row.created_at).getTime(),
        customQuestions: row.custom_questions || [],
      }));
      setJobPostings(postings);

      const newJobApplicantsMap: Record<string, Applicant[]> = {};
      for (const posting of postings) {
        const { data: applicantsData, error: applicantsError } = await supabase
          .from("job_applicants")
          .select("*")
          .eq("opening_id", posting.id);
        if (applicantsError) {
          console.error(`Error fetching applicants for posting ${posting.id}:`, applicantsError);
          newJobApplicantsMap[posting.id] = [];
        } else {
          const applicantsWithSignedUrls = await Promise.all(
            applicantsData.map(async (app: any) => {
              const { data: signedUrlData } = await supabase.storage
                .from("resumes")
                .createSignedUrl(app.resume_url, 3600);
              return {
                id: app.id,
                name: app.applicant_name,
                resumeUrl: signedUrlData?.signedUrl || app.resume_url,
                appliedAt: new Date(app.applied_at).getTime(),
                customAnswers: app.custom_answers || [],
              };
            })
          );
          newJobApplicantsMap[posting.id] = applicantsWithSignedUrls;
        }
      }
      setJobApplicantsMap(newJobApplicantsMap);
    } catch (err) {
      console.error("Error fetching job postings and applicants:", err);
    } finally {
      setJobPostingsLoading(false);
    }
  };

  const handleDeletePosting = async (postingId: string) => {
    try {
      const { error } = await supabase.from("clinic_openings").delete().eq("id", postingId);
      if (error) throw error;
      await fetchJobPostingsAndApplicants();
    } catch (err) {
      console.error("Error deleting posting:", err);
    }
  };

  useEffect(() => {
    if (viewMode === "bookmarks") {
      fetchBookmarkedOpportunities();
    } else if (viewMode === "job-applicants" && jobPostings.length === 0) {
      fetchJobPostingsAndApplicants();
    }
  }, [viewMode]);

  if (authLoading || !profileData) {
    return (
      <div className="flex justify-center items-center h-screen bg-gradient-to-b from-blue-500 to-blue-100">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (profileData.role === "admin") {
    return <AdminView />;
  }

  const navItems = [
    { id: "profile", label: "Profile", icon: <User className="h-5 w-5" /> },
    { id: "bookmarks", label: "Bookmarks", icon: <Bookmark className="h-5 w-5" /> },
    { id: "job-applicants", label: "Job Applicants", icon: <Briefcase className="h-5 w-5" /> },
  ];

  const getPageTitle = () => {
    switch (viewMode) {
      case "profile":
        return "My Profile";
      case "bookmarks":
        return "Saved Opportunities";
      case "job-applicants":
        return "Job Applications";
      case "outreach":
        return "Outreach Dashboard";
      case "outreach-stats":
        return "Outreach Analytics";
      default:
        return "Dashboard";
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-blue-500 to-blue-100">
      {/* Navbar */}
      <Navbar />

      <div className="container mx-auto px-4 py-6 mt-16">
        {/* Mobile Header */}
        <div className="md:hidden flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-white" id="profile-header">
            {getPageTitle()}
          </h1>
          <Button
            variant="outline"
            size="icon"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>

        {/* Mobile Sidebar */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white rounded-lg shadow-xl mb-4 overflow-hidden">
            <div className="p-4 bg-gradient-to-b from-blue-500 to-blue-400">
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12 border-2 border-white">
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-lg font-medium">
                    {profileData.first_name?.[0] || ""}
                    {profileData.last_name?.[0] || ""}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-white">
                    {profileData.first_name} {profileData.last_name}
                  </h3>
                  <p className="text-xs text-blue-100">{profileData.email}</p>
                </div>
              </div>
            </div>
            <div className="p-2" id="profile-nav">
              {navItems.map((item) => (
                <Button
                  key={item.id}
                  variant={viewMode === item.id ? "default" : "ghost"}
                  className={`w-full justify-start mb-1 ${
                    viewMode === item.id ? "bg-blue-600 text-white" : "text-gray-700"
                  }`}
                  onClick={() => {
                    setViewMode(item.id as any);
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.label}
                  {viewMode === item.id && <ChevronRight className="ml-auto h-4 w-4" />}
                </Button>
              ))}
            </div>
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-3">
          {/* Desktop Sidebar */}
          <div className="hidden md:block w-full md:w-60 lg:w-80 flex-shrink-0">
            <Card className="bg-white/95 backdrop-blur-sm shadow-xl overflow-hidden">
              <CardHeader className="bg-gradient-to-b from-blue-500 to-blue-400 text-white p-6">
                <div className="flex flex-col items-center">
                  <Avatar className="h-24 w-24 mb-4 border-4 border-white/30 shadow-lg">
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-3xl font-bold">
                      {profileData.first_name?.[0] || ""}
                      {profileData.last_name?.[0] || ""}
                    </AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-2xl font-bold text-center" id="profile-header">
                    {profileData.first_name} {profileData.last_name}
                  </CardTitle>
                  <CardDescription className="text-blue-100 flex items-center mt-1">
                    <Mail className="h-4 w-4 mr-1.5" />
                    <span className="truncate max-w-[200px]">{profileData.email}</span>
                  </CardDescription>
                  {profileData.location && (
                    <div className="flex items-center text-blue-100 mt-1">
                      <MapPin className="h-4 w-4 mr-1.5" />
                      <span>{profileData.location}</span>
                    </div>
                  )}
                  <div className="mt-4 flex flex-wrap gap-2 justify-center">
                    <Badge className="bg-white/20 hover:bg-white/30 text-white border-transparent">
                      {profileData.education}
                    </Badge>
                    {profileData.school_attended && (
                      <Badge className="bg-white/20 hover:bg-white/30 text-white border-transparent">
                        {profileData.school_attended}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="p-4 bg-blue-50 border-b border-blue-100" id="tokens-display">
                  <TokenDisplay tokens={profileData.tokens} />
                  <ReferralLink />
                </div>
                <div className="p-4">
                  <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-blue-400" />
                    Navigation
                  </h4>
                  <nav className="space-y-1.5" id="profile-nav">
                    {navItems.map((item) => (
                      <Button
                        key={item.id}
                        variant={viewMode === item.id ? "default" : "ghost"}
                        className={`w-full justify-start ${
                          viewMode === item.id ? "bg-blue-600 text-white" : "text-gray-700"
                        }`}
                        onClick={() => setViewMode(item.id as any)}
                      >
                        <span className="mr-3">{item.icon}</span>
                        {item.label}
                        {viewMode === item.id && <ChevronRight className="ml-auto h-4 w-4" />}
                      </Button>
                    ))}
                  </nav>
                </div>
                <Separator />
                <div className="p-4">
                  <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 text-blue-500" />
                    Quick Stats
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Card className="bg-blue-50 border-none">
                      <CardContent className="p-3 flex flex-col items-center justify-center">
                        <FileText className="h-5 w-5 text-blue-600 mb-1" />
                        <span className="text-xs text-gray-600">Resumes</span>
                        <span className="text-lg font-semibold text-blue-700">{resumes?.length || 0}</span>
                      </CardContent>
                    </Card>
                    <Card className="bg-blue-50 border-none">
                      <CardContent className="p-3 flex flex-col items-center justify-center">
                        <Bookmark className="h-5 w-5 text-blue-600 mb-1" />
                        <span className="text-xs text-gray-600">Bookmarks</span>
                        <span className="text-lg font-semibold text-blue-700">
                          {bookmarkedOpportunities?.length || 0}
                        </span>
                      </CardContent>
                    </Card>
                    <Card className="bg-blue-50 border-none">
                      <CardContent className="p-3 flex flex-col items-center justify-center">
                        <Briefcase className="h-5 w-5 text-blue-600 mb-1" />
                        <span className="text-xs text-gray-600">Jobs</span>
                        <span className="text-lg font-semibold text-blue-700">{jobPostings?.length || 0}</span>
                      </CardContent>
                    </Card>
                    <Card className="bg-blue-50 border-none">
                      <CardContent className="p-3 flex flex-col items-center justify-center">
                        <Calendar className="h-5 w-5 text-blue-600 mb-1" />
                        <span className="text-xs text-gray-600">Member Since</span>
                        <span className="text-xs font-semibold text-blue-700">
                          {profileData.created_at ? new Date(profileData.created_at).toLocaleDateString() : "N/A"}
                        </span>
                      </CardContent>
                    </Card>
                  </div>
                </div>
                <Separator />
                <div className="p-4">
                  <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                    <Bell className="h-4 w-4 mr-2 text-blue-500" />
                    Recent Activity
                  </h4>
                  <ScrollArea className="h-32">
                    <div className="space-y-3 pr-3">
                      <div className="flex items-start">
                        <Shield className="h-4 w-4 mr-2 text-blue-400 mt-0.5" />
                        <div className="text-xs">
                          <p className="font-medium">Profile updated</p>
                          <p className="text-gray-500">
                            {profileData.updated_at
                              ? new Date(profileData.updated_at).toLocaleDateString()
                              : "No recent updates"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <Award className="h-4 w-4 mr-2 text-blue-400 mt-0.5" />
                        <div className="text-xs">
                          <p className="font-medium">Member since</p>
                          <p className="text-gray-500">
                            {profileData.created_at ? new Date(profileData.created_at).toLocaleDateString() : "Unknown"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <GraduationCap className="h-4 w-4 mr-2 text-blue-400 mt-0.5" />
                        <div className="text-xs">
                          <p className="font-medium">Education updated</p>
                          <p className="text-gray-500">
                            {profileData.updated_at
                              ? new Date(profileData.updated_at).toLocaleDateString()
                              : "No updates"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <Card className="bg-white/95 backdrop-blur-sm shadow-xl">
              <CardHeader className="border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-2xl font-bold">{getPageTitle()}</CardTitle>
                    <CardDescription>
                      {viewMode === "profile" && "Manage your personal information and resumes"}
                      {viewMode === "bookmarks" && "View and manage your saved opportunities"}
                      {viewMode === "job-applicants" && "Review applications for your job postings"}
                    </CardDescription>
                  </div>
                  {viewMode === "profile" && (
                    <div className="relative">
                      <Button
                        variant="default"
                        onClick={saveProfileData}
                        disabled={saveConfirmation !== "idle"}
                        className={`transition-all duration-300 ${
                          saveConfirmation === "idle"
                            ? "bg-blue-600 hover:bg-blue-700"
                            : saveConfirmation === "saving"
                              ? "bg-blue-400 cursor-wait"
                              : "bg-green-600"
                        }`}
                      >
                        {saveConfirmation === "idle" && "Confirm Changes"}
                        {saveConfirmation === "saving" && (
                          <span className="flex items-center">
                            <svg
                              className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Saving...
                          </span>
                        )}
                        {saveConfirmation === "success" && (
                          <span className="flex items-center">
                            <svg
                              className="-ml-1 mr-2 h-5 w-5 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M5 13l4 4L19 7"
                              ></path>
                            </svg>
                            Saved!
                          </span>
                        )}
                      </Button>
                      {saveConfirmation === "idle" && (
                        <span className="absolute -top-1 -right-1 flex h-3 w-3">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {viewMode === "profile" && (
                  <div id="profile-content">
                    <ProfileContent
                      profileData={profileData}
                      setProfileData={setProfileData}
                      isEditing={isEditing}
                      setIsEditing={setIsEditing}
                      saveProfileData={saveProfileData}
                      resumes={resumes}
                      handleResumeUpload={handleResumeUpload}
                      handleRemoveResume={handleRemoveResume}
                      handleSetDefaultResume={handleSetDefaultResume}
                      activeTab={innerProfileTab}              // Pass controlled inner tab state
                      onTabChange={setInnerProfileTab}
                    />
                  </div>
                )}
                {viewMode === "bookmarks" && (
                  <div id="bookmarks-section">
                    <BookmarksContent
                      isLoadingBookmarks={isLoadingBookmarks}
                      bookmarkedOpportunities={bookmarkedOpportunities}
                    />
                  </div>
                )}
                {viewMode === "job-applicants" && (
                  <div id="job-applicants-section">
                    <JobApplicantsContent
                      jobPostingsLoading={jobPostingsLoading}
                      jobPostings={jobPostings}
                      jobApplicantsMap={jobApplicantsMap}
                      handleDeletePosting={handleDeletePosting}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />

      {/* Render the NextStepJS onboarding popup over the page */}
          {showOnboarding && (
      <OnboardingPopup 
        onComplete={() => setShowOnboarding(false)}
        onStepChange={handleOnboardingStepChange}
      />
    )}

    </div>
  );
}
