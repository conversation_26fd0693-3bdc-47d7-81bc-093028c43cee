'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Footer from '@/components/Footer';
import { CheckCircle, Gift, Award, Sparkles, ArrowRight, Users, Rocket, ExternalLink } from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Image from 'next/image';

const fadeInUpVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerChildren = {
  visible: {
    transition: {
      staggerChildren: 0.2
    }
  }
};

type PartnerCompany = {
  name: string;
  website: string;
  logo?: string;
  description: string;
  offer: string;
  offerLink?: string;
  offerCode?: string;
  bgColor: string;
};

const partnerCompanies: PartnerCompany[] = [
  {
    name: "Crackd",
    website: "crackd.it",
    logo: "/images/crackdlogo.png",
    description: "Crackd is an all-in-one SAT & ACT platform. Create a study plan, practice hard questions, watch video breakdowns, attend live seminars, and get a top 0.1% tutor (whom you can message 24/7), all for the standard cost of one hour of tutoring. Used by over 30,000 students across the world, Crackd has helped students improve scores by up to 660 points and get into every Ivy League school.",
    offer: "10% off",
    offerLink: "https://crackd.it/klinn",
    bgColor: "from-blue-400 to-blue-500"
  },
  {
    name: "Sprint.dev",
    website: "sprint.dev",
    logo: "/images/sprintlogo.png",
    description: "Sprint AI Brainstorming helps you ideate and refine personalized ideas for your next hackathon. Sprint Explain answers any questions about how to get started with hackathons, creating good ideas, or anything else you need help with. Team matching helps you find your ideal teammate based on skills and personality through our builder persona quiz.",
    offer: "Free access to Sprint tools",
    offerLink: "https://www.sprint.dev/klinn",
    bgColor: "from-blue-500 to-blue-600"
  },
  {
    name: "Ora AI",
    website: "oraai.com",
    logo: "/images/oraailogo.png",
    description: "Ora AI is a Y Combinator backed startup trusted by over 22,000 medical students. With over 1,700 topics, Ora comprehensively covers the official 2024 USMLE Content Outline. It is designed for USMLE Step 1, USMLE Step 2, and all eight NBME Shelf exams.",
    offer: "$50 off any subscription",
    offerCode: "KLINN",
    bgColor: "from-blue-600 to-blue-700"
  },
  {
    name: "Runway Mobile App",
    website: "runwayapp.io",
    logo: "/images/runwayAirplaneLogo.webp",
    description: "Runway is an educational app designed to help students identify and pursue their own interests. Students receive a short, 1-2 minute lesson each day on a topic that can be just about anything. Completing the lesson earns streaks and points, which will help students climb up the global leaderboard. Available on both the Apple App Store and Google Play Store.",
    offer: "Free to use for all students",
    bgColor: "from-blue-400 to-blue-500"
  }
];

const StudentScholarsPage = () => {
  useEffect(() => {
    document.title = 'Klinn | Student Scholars Program';
  }, []); 

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-40 pb-32 bg-gradient-to-br from-blue-700 via-blue-600 to-blue-500 relative overflow-hidden">
        {/* <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div> */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <motion.div 
            className="max-w-4xl mx-auto text-center text-white"
            initial="hidden"
            animate="visible"
            variants={staggerChildren}
          >
            <motion.span
              className="inline-block px-4 py-1 mb-8 text-xs font-extralight tracking-wider uppercase bg-white/10 rounded-full backdrop-blur-sm border border-white/10"
              variants={fadeInUpVariants}
            >
              Exclusive Program
            </motion.span>
            <motion.h1 
              className="text-5xl sm:text-6xl font-extralight mb-8 leading-tight tracking-tight"
              variants={fadeInUpVariants}
            >
              Klinn Student Scholars
            </motion.h1>
            <motion.p 
              className="text-xl mb-12 opacity-90 max-w-2xl mx-auto font-extralight tracking-wide"
              variants={fadeInUpVariants}
            >
              Access exclusive benefits, resources, and opportunities from our partner companies to accelerate your healthcare career
            </motion.p>
            
            <motion.div 
              variants={fadeInUpVariants}
              className="flex justify-center"
            >
              <Button 
                className="bg-white/90 text-blue-600 hover:bg-white rounded-full px-8 py-6 font-extralight tracking-wide shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
                onClick={() => window.location.href = '#apply'}
              >
                Apply Now
                <ArrowRight className="ml-2 h-5 w-5 opacity-70" />
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
      
      {/* Main Content */}
      <main className="flex-grow">

                {/* How To Apply */}
                <section id="apply" className="py-24 bg-white">
          <motion.div 
            className="container mx-auto px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 
              className="text-3xl font-extralight mb-16 text-center text-gray-800 tracking-tight"
              variants={fadeInUpVariants}
            >
              How to Become a Klinn Scholar
            </motion.h2>
            
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row gap-12 mb-16">
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.1 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-6 shadow-sm">
                      <span className="text-lg font-extralight text-blue-500">1</span>
                    </div>
                    <h3 className="text-lg font-extralight text-gray-700 mb-3">Apply Online</h3>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Fill out a simple application form and tell us about your <span className="font-semibold text-blue-600">healthcare journey</span>
                    </p>
                  </div>
                </motion.div>
                
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.2 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-6 shadow-sm">
                      <span className="text-lg font-extralight text-blue-500">2</span>
                    </div>
                    <h3 className="text-lg font-extralight text-gray-700 mb-3">Get Approved</h3>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Applications are reviewed within <span className="font-semibold text-blue-600">48 hours</span> with <span className="font-medium">over 90%</span> acceptance rate
                    </p>
                  </div>
                </motion.div>
                
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.3 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-6 shadow-sm">
                      <span className="text-lg font-extralight text-blue-500">3</span>
                    </div>
                    <h3 className="text-lg font-extralight text-gray-700 mb-3">Access Benefits</h3>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Receive your <span className="font-medium">Scholar ID</span> and unlock <span className="font-semibold text-blue-600">all partner benefits</span> instantly
                    </p>
                  </div>
                </motion.div>
              </div>
              
              <motion.div 
                className="text-center"
                variants={fadeInUpVariants}
              >
                <Button 
                  className="bg-blue-600 text-white hover:bg-blue-700 rounded-full px-8 py-6 font-extralight tracking-wide shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
                >
                  Apply to Become a Scholar
                  <ArrowRight className="ml-2 h-5 w-5 opacity-70" />
                </Button>
                <p className="mt-4 text-gray-500 text-sm font-extralight">
                  Applications for the Spring 2025 cohort close on April 15th
                </p>
              </motion.div>
            </div>
          </motion.div>
        </section>
        {/* Program Overview
        <section className="py-24">
          <div className="container mx-auto px-4">
            <motion.div 
              className="max-w-2xl mx-auto text-center mb-16"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.h2 
                className="text-3xl font-extralight mb-5 text-gray-800 tracking-tight"
                variants={fadeInUpVariants}
              >
                Why Join Klinn Student Scholars?
              </motion.h2>
              <motion.p 
                className="text-lg text-gray-500 font-extralight mb-10 tracking-wide"
                variants={fadeInUpVariants}
              >
                Our Student Scholars program connects ambitious pre-med and healthcare students with industry-leading resources and tools. Membership is <span className="font-semibold text-blue-600">free</span> and provides exclusive benefits worth <span className="font-semibold text-blue-600">over $500</span>.
              </motion.p>
            </motion.div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-4xl mx-auto">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.1 }}
              >
                <Card className="h-full border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden">
                  <CardHeader className="text-center pb-2">
                    <Gift className="w-6 h-6 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-lg text-gray-700 font-extralight">Premium Resources</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Access <span className="font-medium">tools and resources</span> typically behind paywalls, <span className="font-semibold text-blue-600">completely free</span> for Klinn Scholars
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.2 }}
              >
                <Card className="h-full border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden">
                  <CardHeader className="text-center pb-2">
                    <Users className="w-6 h-6 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-lg text-gray-700 font-extralight">Exclusive Community</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Join a <span className="font-medium">network</span> of like-minded students and <span className="font-semibold text-blue-600">professionals</span> in the healthcare field
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.3 }}
              >
                <Card className="h-full border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden">
                  <CardHeader className="text-center pb-2">
                    <Rocket className="w-6 h-6 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-lg text-gray-700 font-extralight">Career Acceleration</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center font-extralight tracking-wide">
                      Get a <span className="font-semibold text-blue-600">head start</span> on your healthcare career with <span className="font-medium">specialized tools</span> and mentorship
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </section> */}
        
        {/* Partner Companies */}
        <section className="py-24 bg-white">
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-3xl font-extralight mb-16 text-center text-gray-800 tracking-tight"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Exclusive Partner Benefits
            </motion.h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-5xl mx-auto">
              {partnerCompanies.map((company, index) => (
                <motion.div
                  key={index}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={fadeInUpVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="flex flex-col h-full border-0 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden rounded-xl">
                    <div className={`bg-gradient-to-r ${company.bgColor} p-8 h-36 relative flex flex-col justify-between`}>
                      <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-bl-full"></div>
                      <div>
                        <div className="flex items-center mb-3">
                          <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center mr-3 backdrop-blur-sm overflow-hidden">
                            {company.logo ? (
                              <Image 
                                src={company.logo} 
                                alt={`${company.name} logo`}
                                width={40}
                                height={40}
                                className="object-contain"
                              />
                            ) : (
                              <span className="text-white font-extralight">{company.name.charAt(0)}</span>
                            )}
                          </div>
                          <div>
                            <h3 className="text-lg font-extralight text-white tracking-wide">{company.name}</h3>
                            <a href={`https://${company.website}`} className="text-xs text-blue-100 font-extralight flex items-center" target="_blank" rel="noopener noreferrer">
                              {company.website}
                              <ExternalLink className="ml-1 h-3 w-3" />
                            </a>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-auto">
                        <div className="text-sm font-extralight text-white flex items-center">
                          <Award className="w-4 h-4 mr-2 opacity-80" />
                          Scholar Offer: {company.offer}
                        </div>
                      </div>
                    </div>
                    
                    <CardContent className="p-8 flex flex-col flex-grow">
                      <div className="flex-grow">
                        <p className="text-gray-600 font-extralight tracking-wide mb-6">
                          {company.name === "Crackd" ? (
                            <>
                              Crackd is an <span className="font-semibold text-blue-600">all-in-one SAT & ACT platform</span>. Create a study plan, practice hard questions, watch video breakdowns, attend live seminars, and get a <span className="font-semibold text-blue-600">top 0.1% tutor</span> (whom you can message <span className="font-medium">24/7</span>), all for the standard cost of one hour of tutoring. Used by <span className="font-semibold text-blue-600">over 30,000 students</span> across the world, Crackd has helped students improve scores by up to <span className="font-semibold text-blue-600">660 points</span> and get into <span className="font-medium">every Ivy League school</span>.
                            </>
                          ) : company.name === "Sprint.dev" ? (
                            <>
                              <span className="font-semibold text-blue-600">Sprint AI Brainstorming</span> helps you ideate and refine <span className="font-medium">personalized ideas</span> for your next hackathon. <span className="font-semibold text-blue-600">Sprint Explain</span> answers any questions about how to get started with hackathons, how to make a good idea, or anything else you have on your mind. <span className="font-semibold text-blue-600">Team matching</span> helps you find your <span className="font-medium">ideal teammate</span> based on skills and personality through our builder persona quiz.
                            </>
                          ) : company.name === "Ora AI" ? (
                            <>
                              Ora AI is a <span className="font-semibold text-blue-600">Y Combinator backed</span> startup trusted by <span className="font-semibold text-blue-600">over 22,000 medical students</span>. With <span className="font-medium">over 1,700 topics</span>, Ora comprehensively covers the official <span className="font-medium">2024 USMLE Content Outline</span>. It is designed for <span className="font-semibold text-blue-600">USMLE Step 1, USMLE Step 2</span>, and all eight <span className="font-medium">NBME Shelf exams</span>.
                            </>
                          ) : (
                            <>
                              Runway is an educational app designed to help students <span className="font-semibold text-blue-600">identify and pursue their own interests</span>. Students receive a short, <span className="font-medium">1-2 minute lesson each day</span> on a topic that can be just about anything. Completing the lesson earns <span className="font-semibold text-blue-600">streaks and points</span>, which will help students climb up the <span className="font-medium">global leaderboard</span>. Available on both the <span className="font-medium">Apple App Store and Google Play Store</span>.
                            </>
                          )}
                        </p>
                      </div>
                      
                      <div className="mt-8">
                        <div className="mt-auto">
                          {company.offerLink && (
                            <Button 
                              className="w-full rounded-full text-sm h-11 font-extralight tracking-wide shadow-sm hover:shadow-md transition-all duration-300 bg-blue-500 hover:bg-blue-600"
                              onClick={() => window.open(company.offerLink, '_blank')}
                            >
                              Claim Offer
                              <ArrowRight className="ml-1 h-4 w-4 opacity-70" />
                            </Button>
                          )}
                          
                          {company.offerCode && (
                            <div className="text-center">
                              <p className="text-gray-500 text-sm font-extralight mb-2">Use code:</p>
                              <div className="bg-gray-100 py-2 px-4 rounded-md font-mono text-blue-600 text-base tracking-wider">
                                {company.offerCode}
                              </div>
                            </div>
                          )}
                          
                          {!company.offerLink && !company.offerCode && (
                            <Button 
                              className="w-full rounded-full text-sm h-11 font-extralight tracking-wide shadow-sm hover:shadow-md transition-all duration-300 bg-blue-500 hover:bg-blue-600"
                            >
                              Available for All Scholars
                              <CheckCircle className="ml-1 h-4 w-4 opacity-70" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      
        
        {/* FAQ Section */}
        {/* <section className="py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-3xl font-extralight mb-16 text-center text-gray-800 tracking-tight"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Frequently Asked Questions
            </motion.h2>
            
            <div className="max-w-3xl mx-auto">
              <div className="space-y-6">
                {[
                  {
                    question: "Is the Klinn Student Scholars program really free?",
                    answer: "Yes! There is absolutely no cost to join or maintain your Klinn Scholar status. We've partnered with companies who want to support future healthcare professionals."
                  },
                  {
                    question: "Who is eligible to become a Klinn Scholar?",
                    answer: "Any student pursuing or interested in a healthcare career is eligible. This includes high school students, undergraduates, post-baccalaureate students, and graduate students."
                  },
                  {
                    question: "How long do the benefits last?",
                    answer: "Most benefits are valid for 6-12 months from activation. You can renew your Scholar status annually as long as you remain a student."
                  },
                  {
                    question: "Can I share my benefits with friends?",
                    answer: "Scholar benefits are non-transferable and linked to your individual account. However, you can refer friends to apply for their own Scholar membership!"
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    variants={fadeInUpVariants}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="border-0 shadow-md hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg text-gray-700 font-extralight">{faq.question}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-500 font-extralight tracking-wide">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
              
              <motion.div 
                className="mt-12 text-center"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
              >
                <p className="text-gray-600 mb-4 font-extralight">
                  Have more questions? We're here to help!
                </p>
                <Link href="/contact" passHref>
                  <Button 
                    variant="outline" 
                    className="border-blue-300 text-blue-600 hover:bg-blue-50 font-extralight tracking-wide rounded-full"
                  >
                    Contact Support
                  </Button>
                </Link>
              </motion.div>
            </div>
          </div>
        </section> */}
        
        {/* Call to Action */}
        <section className="py-24 bg-gray-50 text-blue-600">
          <motion.div 
            className="container mx-auto px-4 text-center"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 
              className="text-3xl font-extralight mb-5 tracking-tight"
              variants={fadeInUpVariants}
            >
              Join Over 0 Klinn Scholars Today
            </motion.h2>
            <motion.p 
              className="text-lg mb-10 max-w-xl mx-auto opacity-90 font-extralight tracking-wide"
              variants={fadeInUpVariants}
            >
              Take the next step in your healthcare journey with <span className="font-semibold">exclusive resources</span>, <span className="font-medium">tools</span>, and <span className="font-semibold">opportunities</span>
            </motion.p>
            <motion.div 
              className="flex flex-wrap justify-center gap-5"
              variants={fadeInUpVariants}
            >
              <Button 
                className="bg-blue-600 text-white hover:bg-white hover:text-blue-600 rounded-full px-6 py-2 font-extralight tracking-wide shadow-md hover:shadow-lg transition-all duration-300"
                onClick={() => window.location.href = '#apply'}
              >
                Apply Now
                <ArrowRight className="ml-1 h-4 w-4 opacity-70" />
              </Button>
            </motion.div>
          </motion.div>
        </section>
      </main>

      <Footer className="bg-blue-600 text-white py-10" />
    </div>
  );
};

export default StudentScholarsPage;