'use client';

import React, { useState, useEffect, ChangeEvent, FormEvent, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Briefcase,
  Clock,
  MapPin,
  DollarSign,
  X,
  ChevronRight,
  Building2,
  Calendar,
  Filter,
  Microscope,
  GraduationCap,
  HeartHandshake,
  Stethoscope,
  Flag,
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../../supabase/supabaseClient';
import ReportModal from './ReportModal';

// ----- TYPES -----
interface ClinicOpening {
  id: string;
  clinic_name: string;
  address: string;
  city: string;
  state: string;
  position_title: string;
  time_commitment: string;
  availability: string;
  compensation: string;
  description: string;
  created_at: string;
  type: string;
  custom_questions?: string[];
  startupCategory?: string;
}

interface JobCardProps {
  opening: ClinicOpening;
  onApply: (opening: ClinicOpening) => void;
  onClick: () => void;
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  compensation: string;
  postedAt: number;
  customQuestions?: string[];
}

interface Applicant {
  id: string;
  name: string;
  resumeUrl: string;
  appliedAt: number;
  customAnswers?: string[];
}

// ----- UTILITY FUNCTIONS FOR CARD STYLING -----
const getTypeColor = (type: string) => {
  const colors = {
    internship: { 
      bg: 'bg-blue-50', 
      text: 'text-blue-700', 
      border: 'border-blue-300',
      iconBg: 'bg-blue-100',
      gradientFrom: 'from-blue-50',
      gradientTo: 'to-white' 
    },
    shadowing: { 
      bg: 'bg-purple-50', 
      text: 'text-purple-700', 
      border: 'border-purple-300',
      iconBg: 'bg-purple-100',
      gradientFrom: 'from-purple-50',
      gradientTo: 'to-white'
    },
    research: { 
      bg: 'bg-emerald-50', 
      text: 'text-emerald-700', 
      border: 'border-emerald-300',
      iconBg: 'bg-emerald-100',
      gradientFrom: 'from-emerald-50',
      gradientTo: 'to-white'
    },
    volunteer: { 
      bg: 'bg-orange-50', 
      text: 'text-orange-700', 
      border: 'border-orange-300',
      iconBg: 'bg-orange-100',
      gradientFrom: 'from-orange-50',
      gradientTo: 'to-white'
    },
    startup: { 
      bg: 'bg-indigo-50', 
      text: 'text-indigo-700', 
      border: 'border-indigo-300',
      iconBg: 'bg-indigo-100',
      gradientFrom: 'from-indigo-50',
      gradientTo: 'to-white'
    },
    'healthcare policy/admin': { 
      bg: 'bg-red-50', 
      text: 'text-red-700', 
      border: 'border-red-300',
      iconBg: 'bg-red-100',
      gradientFrom: 'from-red-50',
      gradientTo: 'to-white'
    },
  };
  return colors[type as keyof typeof colors] || { 
    bg: 'bg-gray-50', 
    text: 'text-gray-700', 
    border: 'border-gray-300',
    iconBg: 'bg-gray-100',
    gradientFrom: 'from-gray-50',
    gradientTo: 'to-white'
  };
};

const getTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'research':
      return Microscope;
    case 'internship':
      return GraduationCap;
    case 'volunteer':
      return HeartHandshake;
    case 'shadowing':
      return Stethoscope;
    default:
      return Briefcase;
  }
};

const getStripeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case 'research':
      return 'bg-emerald-700';
    case 'internship':
      return 'bg-blue-700';
    case 'volunteer':
      return 'bg-orange-700';
    case 'shadowing':
      return 'bg-purple-700';
    case 'startup':
      return 'bg-indigo-700';
    case 'healthcare policy/admin':
      return 'bg-red-700';
    default:
      return 'bg-gray-700';
  }
};

const getStripeGlowColor = (type: string, opacity: number = 0.4) => {
  switch (type.toLowerCase()) {
    case 'research':
      return `rgba(16, 185, 129, ${opacity})`;
    case 'internship':
      return `rgba(59, 130, 246, ${opacity})`;
    case 'volunteer':
      return `rgba(249, 115, 22, ${opacity})`;
    case 'shadowing':
      return `rgba(168, 85, 247, ${opacity})`;
    case 'startup':
      return `rgba(99, 102, 241, ${opacity})`;
    case 'healthcare policy/admin':
      return `rgba(239, 68, 68, ${opacity})`;
    default:
      return `rgba(107, 114, 128, ${opacity})`;
  }
};

// ----- JOB CARD COMPONENT (memoized) -----
const JobCard: React.FC<JobCardProps> = memo(({ opening, onApply, onClick }) => {
  const typeColors = getTypeColor(opening.type);
  const TypeIcon = getTypeIcon(opening.type);
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ 
        scale: 1.03,
        boxShadow: "0 20px 30px -12px rgba(0, 0, 0, 0.15), 0 10px 20px -8px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2 }
      }}
      className="relative overflow-hidden bg-white rounded-xl shadow-lg transition-all duration-300 cursor-pointer group"
      onClick={onClick}
    >
      {/* Left stripe with glow */}
      <div className="absolute left-0 top-0 bottom-0 w-3 overflow-hidden">
        <div 
          className={`absolute inset-0 ${getStripeColor(opening.type)} opacity-90`}
          style={{ boxShadow: `0 0 20px 5px ${getStripeGlowColor(opening.type)}` }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-b from-white/30 via-transparent to-white/20"></div>
      </div>
  
      {/* Background gradient pattern */}
      <div 
        className={`absolute inset-0 opacity-10 bg-gradient-to-br ${getTypeColor(opening.type).gradientFrom} ${getTypeColor(opening.type).gradientTo}`}
        style={{ clipPath: "polygon(0 0, 100% 0, 70% 100%, 0 80%)" }}
      ></div>
  
      <div className="pl-6 p-6 relative">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-start gap-3">
            <motion.div 
              className={`${getTypeColor(opening.type).iconBg} p-2.5 rounded-lg shadow-md relative`}
              whileHover={{ rotate: 5, scale: 1.1 }}
              style={{ boxShadow: `0 0 10px ${getStripeGlowColor(opening.type, 0.15)}` }}
            >
              <div className="absolute inset-0 bg-gradient-to-tr from-white/40 to-transparent rounded-lg"></div>
              <TypeIcon className={getTypeColor(opening.type).text} size={24} />
            </motion.div>
            <div>
              <h3 className="text-xl font-bold text-gray-800 mb-2 transition-colors">
                <span className="transition-all duration-200 group-hover:text-blue-600 group-hover:underline">
                  {opening.position_title}
                </span>
              </h3>
              <div className="flex items-center gap-2">
                <Building2 size={16} className="text-gray-500" />
                <p className="text-lg text-gray-600">{opening.clinic_name}</p>
              </div>
            </div>
          </div>
          <span className={`px-4 py-1.5 rounded-full text-sm font-medium capitalize ${getTypeColor(opening.type).bg} ${getTypeColor(opening.type).text} shadow-sm transition-shadow relative overflow-hidden group-hover:shadow-md`}>
            <span className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent"></span>
            {opening.type}
          </span>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
          <InfoIcon 
            icon={MapPin} 
            text={`${opening.city}, ${opening.state}`}
            bgColor={getTypeColor(opening.type).iconBg}
            glowColor={getStripeGlowColor(opening.type, 0.1)}
          />
          <InfoIcon 
            icon={Clock} 
            text={opening.time_commitment}
            bgColor={getTypeColor(opening.type).iconBg}
            glowColor={getStripeGlowColor(opening.type, 0.1)}
          />
          <InfoIcon 
            icon={DollarSign} 
            text={opening.compensation}
            bgColor={getTypeColor(opening.type).iconBg}
            glowColor={getStripeGlowColor(opening.type, 0.1)}
          />
          <InfoIcon 
            icon={Calendar} 
            text={new Date(opening.created_at).toLocaleDateString()}
            bgColor={getTypeColor(opening.type).iconBg}
            glowColor={getStripeGlowColor(opening.type, 0.1)}
          />
        </div>
  
        <p className="text-gray-500 mb-6 line-clamp-2 transition-all duration-300">
          {opening.description}
        </p>
  
        <div className="flex justify-between items-center">
          <motion.button
            whileHover={{ scale: 1.05, backgroundColor: "#047857" }}
            whileTap={{ scale: 0.98 }}
            onClick={(e) => {
              e.stopPropagation();
              onApply(opening);
            }}
            className="px-6 py-2.5 text-white rounded-lg font-medium transition-all duration-200 bg-emerald-600 shadow-sm hover:shadow-md"
          >
            Quick Apply
          </motion.button>
          <motion.div 
            className={`${getTypeColor(opening.type).iconBg} p-2 rounded-full transition-transform shadow-md`}
            whileHover={{ scale: 1.2, rotate: 90, backgroundColor: getTypeColor(opening.type).bg }}
            animate={{ scale: [1, 1.1, 1], rotate: [0, 5, 0] }}
            transition={{ duration: 0.5, repeat: Infinity, repeatType: "reverse", repeatDelay: 2 }}
            style={{ boxShadow: `0 0 8px ${getStripeGlowColor(opening.type, 0.15)}` }}
          >
            <ChevronRight className={getTypeColor(opening.type).text} />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
});

JobCard.displayName = 'JobCard';

const InfoIcon = ({ 
  icon: Icon, 
  text, 
  bgColor,
  glowColor 
}: { 
  icon: React.ElementType; 
  text: string; 
  bgColor: string;
  glowColor: string;
}) => (
  <div className="flex items-center gap-2 group/info">
    <div className={`${bgColor} p-2 rounded-lg relative overflow-hidden transition-all duration-300 group-hover/info:scale-105`}
      style={{ boxShadow: `0 0 8px ${glowColor}` }}
    >
      <span className="absolute inset-0 bg-gradient-to-tr from-white/40 to-transparent"></span>
      <Icon size={16} className="group-hover/info:scale-110 transition-transform duration-300" />
    </div>
    <span className="text-gray-600 transition-all duration-300 group-hover/info:text-gray-800">{text}</span>
  </div>
);

// ----- JOB BOARD COMPONENT (Supabase version) -----
const JobBoard: React.FC = () => {
  const { user } = useAuth();
  const [clinicOpenings, setClinicOpenings] = useState<ClinicOpening[]>([]);
  const [baseOpenings, setBaseOpenings] = useState<ClinicOpening[]>([]);
  const [filteredOpenings, setFilteredOpenings] = useState<ClinicOpening[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  // Filter states
  const [selectedType, setSelectedType] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedTimeCommitment, setSelectedTimeCommitment] = useState('');
  const [selectedCompensation, setSelectedCompensation] = useState('');
  const [selectedStartupCategory, setSelectedStartupCategory] = useState('');
  // Modal & Quick Apply states
  const [showMoreFiltersModal, setShowMoreFiltersModal] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [reportingJob, setReportingJob] = useState<{id: string, clinic_name: string} | null>(null);
  const [showCustomQuestionsModal, setShowCustomQuestionsModal] = useState(false);
  const [currentApplication, setCurrentApplication] = useState<ClinicOpening | null>(null);
  const [customAnswers, setCustomAnswers] = useState<string[]>([]);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [jobApplicantsMap, setJobApplicantsMap] = useState<Record<string, Applicant[]>>({});
  const [jobPostingsLoading, setJobPostingsLoading] = useState<boolean>(false);

  // Extended type options
  const typeOptions = ['internship', 'shadowing', 'research', 'volunteer', 'startup', 'healthcare policy/admin'];
  const startupOptions = ['internship', 'volunteering', 'research', 'shadow', 'nonprofit'];
  
  // For filters (derive location, time commitment, compensation from fetched data)
  const locationOptions = Array.from(new Set(clinicOpenings.map((o) => o.state))).sort();
  const timeCommitmentOptions = Array.from(new Set(clinicOpenings.map((o) => o.time_commitment))).sort();
  const compensationOptions = Array.from(new Set(clinicOpenings.map((o) => o.compensation))).sort();

  // ----- FETCH APPROVED OPENINGS FROM SUPABASE -----
  const fetchApprovedOpenings = async () => {
    setIsLoading(true);
    const { data: openingsData, error } = await supabase
      .from('clinic_openings')
      .select('*')
      .eq('status', 'approved');
    if (error) {
      console.error('Error fetching approved openings:', error);
    } else if (openingsData) {
      setClinicOpenings(openingsData as ClinicOpening[]);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchApprovedOpenings();
  }, []);

  // ----- FILTERING LOGIC -----
  const filterOpenings = () => {
    let base = clinicOpenings;
    if (searchTerm) {
      const lowercasedTerm = searchTerm.toLowerCase();
      base = base.filter(
        (opening) =>
          opening.position_title.toLowerCase().includes(lowercasedTerm) ||
          opening.clinic_name.toLowerCase().includes(lowercasedTerm) ||
          opening.description.toLowerCase().includes(lowercasedTerm) ||
          opening.type.toLowerCase().includes(lowercasedTerm)
      );
    }
    if (selectedLocation) base = base.filter((opening) => opening.state === selectedLocation);
    if (selectedTimeCommitment)
      base = base.filter((opening) => opening.time_commitment === selectedTimeCommitment);
    if (selectedCompensation)
      base = base.filter((opening) => opening.compensation === selectedCompensation);
    if (selectedStartupCategory)
      base = base.filter((opening) => opening.startupCategory === selectedStartupCategory);
    
    setBaseOpenings(base);
    
    const final = selectedType ? base.filter((opening) => opening.type === selectedType) : base;
    setFilteredOpenings(final);
  };

  useEffect(() => {
    filterOpenings();
  }, [
    searchTerm,
    selectedLocation,
    selectedTimeCommitment,
    selectedCompensation,
    selectedStartupCategory,
    selectedType,
    clinicOpenings,
  ]);

  // ----- QUICK APPLY & APPLICATION SUBMISSION (SUPABASE) -----
  const handleQuickApply = async (opening: ClinicOpening) => {
    if (!user) {
      alert('Please sign in to apply.');
      return;
    }
    if (opening.custom_questions && opening.custom_questions.length > 0) {
      setCurrentApplication(opening);
      setCustomAnswers(opening.custom_questions.map(() => ''));
      setShowCustomQuestionsModal(true);
      return;
    }
    await submitApplication(opening, []);
  };

  const submitApplication = async (opening: ClinicOpening, answers: string[]) => {
    try {
      // Retrieve user profile from Supabase to ensure a default resume is set
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user!.id)
        .maybeSingle();
      if (profileError || !profileData) {
        alert('User profile not found. Please update your profile.');
        return;
      }
      if (!profileData.default_resume_url) {
        alert('Please set a default resume in your profile before applying.');
        return;
      }
      // Check if the applicant has already applied for this opening
      const { data: existingApps, error: existingError } = await supabase
      .from('job_applicants')
      .select('*')
      .eq('opening_id', opening.id)
      .eq('applicant_id', user!.id);
      if (existingError) {
        console.error('Error checking existing applications:', existingError);
        return;
      }
      if (existingApps && existingApps.length > 0) {
        alert('You have already applied to this job.');
        return;
      }
      // Insert
    const applicationData = {
      opening_id: opening.id,
      applicant_id: user!.id,
      applicant_name: `${profileData.first_name} ${profileData.last_name}`,
      resume_url: profileData.default_resume_url,
      applied_at: new Date().toISOString(),
      custom_answers: answers,
    };

    const { error: insertError } = await supabase
      .from('job_applicants')
      .insert(applicationData);
      if (insertError) throw insertError;
      alert('Application submitted successfully!');
      setShowCustomQuestionsModal(false);
      setCurrentApplication(null);
    } catch (error) {
      console.error('Error applying to job:', error);
      alert('There was an error submitting your application. Please try again.');
    }
  };

  const handleAnswerChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const newAnswers = [...customAnswers];
    newAnswers[index] = e.target.value;
    setCustomAnswers(newAnswers);
  };

  const handleCustomSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!currentApplication) return;
    await submitApplication(currentApplication, customAnswers);
    setShowCustomQuestionsModal(false);
    setCurrentApplication(null);
  };

  const cancelCustomQuestions = () => {
    setShowCustomQuestionsModal(false);
    setCurrentApplication(null);
  };

  // ----- FETCH JOB POSTINGS & APPLICANTS (SUPABASE) -----
  const fetchJobPostingsAndApplicants = async () => {
    if (!user) return;
    setJobPostingsLoading(true);
    // Fetch approved postings belonging to the current user
    const { data: postingsData, error: postingsError } = await supabase
      .from('clinic_openings')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'approved');
    if (postingsError) {
      console.error('Error fetching job postings:', postingsError);
      setJobPostingsLoading(false);
      return;
    }
    const postings: JobPosting[] = (postingsData as any[]).map((row) => ({
      id: row.id,
      title: row.position_title,
      description: row.description,
      compensation: row.compensation,
      postedAt: new Date(row.created_at).getTime(),
      customQuestions: row.custom_questions || [],
    }));
    setJobPostings(postings);

    const newJobApplicantsMap: Record<string, Applicant[]> = {};
    for (const posting of postings) {
      const { data: applicantsData, error: applicantsError } = await supabase
        .from('job_applicants')
        .select('*')
        .eq('opening_id', posting.id);
      if (applicantsError) {
        console.error(`Error fetching applicants for posting ${posting.id}:`, applicantsError);
        newJobApplicantsMap[posting.id] = [];
      } else {
        newJobApplicantsMap[posting.id] = (applicantsData as any[]).map((app) => ({
          id: app.id,
          name: app.applicant_name,
          resumeUrl: app.resume_url,
          appliedAt: new Date(app.applied_at).getTime(),
          customAnswers: app.custom_answers || [],
        }));
      }
    }
    setJobApplicantsMap(newJobApplicantsMap);
    setJobPostingsLoading(false);
  };

  // ----- MODAL STATE & HANDLERS -----
  const [selectedJob, setSelectedJob] = useState<ClinicOpening | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenReport = (e: React.MouseEvent, jobId: string, clinicName: string) => {
    e.stopPropagation();
    setReportingJob({ id: jobId, clinic_name: clinicName });
    setIsReportModalOpen(true);
  };

  const handleCloseReport = () => {
    setIsReportModalOpen(false);
    setTimeout(() => {
      setReportingJob(null);
    }, 300);
  };


  const handleOpenModal = (job: ClinicOpening) => {
    setSelectedJob(job);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setTimeout(() => {
      setSelectedJob(null);
    }, 300);
  };

  const DetailedModal = ({
    job,
    onClose,
  }: {
    job: ClinicOpening;
    onClose: () => void;
  }) => (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
      onClick={onClose}
    >
      <motion.div
        initial={{ x: '100%' }}
        animate={{ x: 0 }}
        exit={{ x: '100%' }}
        transition={{ type: 'tween', duration: 0.3 }}
        className="w-full max-w-2xl bg-white h-full overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-8">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">{job.position_title}</h2>
              <p className="text-xl font-medium text-gray-600">{job.clinic_name}</p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => handleOpenReport(e, job.id, job.clinic_name)}
                className="text-gray-600 hover:text-red-500 flex items-center gap-1 text-sm p-2 rounded-md hover:bg-red-50 transition-colors"
              >
                <Flag className="w-4 h-4" />
                Report
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="text-gray-500" />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-8">
            <div className="flex items-center p-4 bg-gray-50 rounded-lg">
              <Building2 className="text-emerald-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Location</p>
                <p className="font-medium">{`${job.city}, ${job.state}`}</p>
              </div>
            </div>
            <div className="flex items-center p-4 bg-gray-50 rounded-lg">
              <Briefcase className="text-emerald-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Position Type</p>
                <p className="font-medium capitalize">{job.type}</p>
              </div>
            </div>
            <div className="flex items-center p-4 bg-gray-50 rounded-lg">
              <Clock className="text-emerald-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Time Commitment</p>
                <p className="font-medium">{job.time_commitment}</p>
              </div>
            </div>
            <div className="flex items-center p-4 bg-gray-50 rounded-lg">
              <DollarSign className="text-emerald-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Compensation</p>
                <p className="font-medium">{job.compensation}</p>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-bold text-gray-800 mb-4">Description</h3>
            <p className="text-gray-500 whitespace-pre-line">{job.description}</p>
          </div>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleQuickApply(job)}
            className="w-full py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg"
          >
            Apply Now
          </motion.button>
        </div>
      </motion.div>
    </div>
  );

  // ----- FILTER PILL COMPONENT -----
  const FilterPill = ({
    label,
    isActive,
    onClick,
    count,
  }: {
    label: string;
    isActive: boolean;
    onClick: () => void;
    count?: number;
  }) => (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`${
        isActive
          ? 'bg-emerald-100 text-emerald-700 border-emerald-200'
          : 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50'
      } px-4 py-2 rounded-full border transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md`}
    >
      <span className="capitalize">{label}</span>
      {count !== undefined && (
        <span className={`${isActive ? 'bg-emerald-200' : 'bg-gray-100'} px-2 py-0.5 rounded-full text-sm`}>
          {count}
        </span>
      )}
    </motion.button>
  );

  return (
    <div className="min-h-screen">
      {/* HERO SECTION */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center py-16 px-4"
      >
        <h1 className="text-5xl sm:text-6xl font-bold text-white mb-4 mt-20">
          Discover Medical Opportunities
        </h1>
        <p className="text-xl text-white/90">
          Explore clinical experiences that accelerate your career.
        </p>
      </motion.div>

      {reportingJob && (
        <ReportModal
          openingId={reportingJob.id}
          clinicName={reportingJob.clinic_name}
          onClose={handleCloseReport}
          isOpen={isReportModalOpen}
        />
      )}

      <div className="container mx-auto px-4 max-w-7xl">
        {/* SEARCH & FILTERS */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8 space-y-6"
        >
          <div className="relative group">
            <input
              type="text"
              placeholder="Search opportunities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border-0 focus:ring-2 focus:ring-emerald-400 focus:border-transparent text-lg transition-shadow duration-300"
            />
            <Search
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-emerald-600 transition-transform duration-300"
              size={24}
            />
          </div>

          <div className="flex flex-wrap gap-3">
            <FilterPill
              label="All Types"
              isActive={!selectedType}
              onClick={() => setSelectedType('')}
              count={baseOpenings.length}
            />
            {typeOptions.slice(0, 4).map((type) => (
              <FilterPill
                key={type}
                label={type}
                isActive={selectedType === type}
                onClick={() => setSelectedType(type)}
                count={baseOpenings.filter(o => o.type === type).length}
              />
            ))}
            <motion.button
              whileHover={{ scale: 1.05, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowMoreFiltersModal(true)}
              className="px-4 py-2 rounded-full border border-white/20 bg-white/90 backdrop-blur-sm text-emerald-700 hover:bg-white transition-all duration-200 flex items-center gap-2 shadow-sm hover:shadow-md"
            >
              <Filter size={16} />
              More Filters
            </motion.button>
          </div>
        </motion.div>

        {/* JOB CARDS */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="rounded-full h-12 w-12 border-4 border-white border-t-transparent"
            ></motion.div>
          </div>
        ) : filteredOpenings.length === 0 ? (
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border-0 p-8 text-center"
          >
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              No opportunities found
            </h3>
            <p className="text-gray-600">
              Try adjusting your search filters to find more opportunities.
            </p>
          </motion.div>
        ) : (
          <AnimatePresence>
            <motion.div className="grid gap-6" transition={{ staggerChildren: 0.1 }}>
              {filteredOpenings.map((opening) => (
                <JobCard
                  key={opening.id}
                  opening={opening}
                  onApply={handleQuickApply}
                  onClick={() => handleOpenModal(opening)}
                />
              ))}
            </motion.div>
          </AnimatePresence>
        )}
      </div>

      {/* MORE FILTERS MODAL */}
      <AnimatePresence>
        {showMoreFiltersModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl max-w-lg w-full m-4 p-6"
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800">More Filters</h2>
                <button onClick={() => setShowMoreFiltersModal(false)}>
                  <X className="text-gray-500" size={20} />
                </button>
              </div>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  setShowMoreFiltersModal(false);
                }}
              >
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Opportunity Type
                  </label>
                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="">All</option>
                    {typeOptions.map((type) => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Location
                  </label>
                  <select
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="">All</option>
                    {locationOptions.map((loc) => (
                      <option key={loc} value={loc}>{loc}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Time Commitment
                  </label>
                  <select
                    value={selectedTimeCommitment}
                    onChange={(e) => setSelectedTimeCommitment(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="">All</option>
                    {timeCommitmentOptions.map((time) => (
                      <option key={time} value={time}>{time}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Compensation
                  </label>
                  <select
                    value={selectedCompensation}
                    onChange={(e) => setSelectedCompensation(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="">All</option>
                    {compensationOptions.map((comp) => (
                      <option key={comp} value={comp}>{comp}</option>
                    ))}
                  </select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Startups
                  </label>
                  <select
                    value={selectedStartupCategory}
                    onChange={(e) => setSelectedStartupCategory(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500"
                  >
                    <option value="">All</option>
                    {startupOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
                <div className="flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setShowMoreFiltersModal(false)}
                    className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-emerald-600 text-white rounded-md"
                  >
                    Apply Filters
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* DETAILED MODAL */}
      <AnimatePresence>
        {isModalOpen && selectedJob && (
          <DetailedModal job={selectedJob} onClose={handleCloseModal} />
        )}
      </AnimatePresence>

      {/* CUSTOM QUESTIONS MODAL */}
      <AnimatePresence>
        {showCustomQuestionsModal && currentApplication && currentApplication.custom_questions && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl max-w-md w-full m-4 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">
                    Additional Questions
                  </h2>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    onClick={() => {
                      setShowCustomQuestionsModal(false);
                      setCurrentApplication(null);
                    }}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="text-gray-500" size={20} />
                  </motion.button>
                </div>
                <form onSubmit={handleCustomSubmit}>
                  <div className="space-y-4 mb-6">
                    {currentApplication.custom_questions.map((question, index) => (
                      <motion.div 
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {question}
                        </label>
                        <input
                          type="text"
                          value={customAnswers[index] || ''}
                          onChange={(e) => handleAnswerChange(e, index)}
                          className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-shadow hover:shadow-md"
                          required
                        />
                      </motion.div>
                    ))}
                  </div>
                  <div className="flex gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      type="button"
                      onClick={cancelCustomQuestions}
                      className="flex-1 py-2 px-4 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05, backgroundColor: "#047857" }}
                      whileTap={{ scale: 0.95 }}
                      type="submit"
                      className="flex-1 py-2 px-4 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors shadow-md hover:shadow-lg"
                    >
                      Submit Application
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default JobBoard;
