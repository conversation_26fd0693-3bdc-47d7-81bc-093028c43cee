"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, Mail, MapPin, Coins, Check, Lock } from "lucide-react";

export interface StartupContactDetails {
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  startup: string;
  website?: string;
  hq?: string;
  startupId: string;
}

interface StartupContactCardProps {
  contact: StartupContactDetails;
  onSelect: (selectedContact: StartupContactDetails) => void;
  requiresToken?: boolean;
  showTokenCost?: boolean;
  isSelected?: boolean;
  isUnlocked?: boolean;
  firstContactSelected?: boolean;
}

const StartupContactCard: React.FC<StartupContactCardProps> = ({
  contact,
  onSelect,
  requiresToken = false,
  showTokenCost = false,
  isSelected = false,
  isUnlocked = false,
  firstContactSelected = false,
}) => {
  const [acknowledged, setAcknowledged] = useState(false);
  const hasName = contact.firstName || contact.lastName;

  const handleSelect = () => {
    onSelect(contact);
    if (requiresToken) {
      setAcknowledged(true);
      setTimeout(() => setAcknowledged(false), 2000);
    }
  };

  const getButtonText = () => {
    if (isSelected) {
      return (
        <div className="flex items-center">
          <Check className="h-4 w-4 mr-1.5" />
          Selected
        </div>
      );
    }
    if (acknowledged) {
      return (
        <div className="flex items-center">
          <Coins className="h-4 w-4 mr-1.5" />
          Token Deducted
        </div>
      );
    }
    if (requiresToken || (firstContactSelected && !isUnlocked)) {
      return (
        <div className="flex items-center">
          <Lock className="h-4 w-4 mr-1.5" />
          Unlock (1 Token)
        </div>
      );
    }
    return (
      <div className="flex items-center">
        <Mail className="h-4 w-4 mr-1.5" />
        Contact
      </div>
    );
  };

  return (
    <Card className={`border transition-colors ${
      isSelected 
        ? "border-green-400 bg-green-50" 
        : "border-blue-200 hover:border-blue-400"
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center shrink-0 border border-blue-100">
            <User className="h-5 w-5 text-blue-600" />
          </div>
          <div className="flex-1 min-w-0">
            {hasName && (
              <h4 className="font-medium text-gray-800 truncate">
                {contact.firstName || ""} {contact.lastName || ""}
              </h4>
            )}
            {contact.role && (
              <p className="text-sm text-gray-600 truncate">{contact.role}</p>
            )}
            <div className="mt-2">
              <Button
                className={`w-full ${
                  isSelected 
                    ? "bg-green-600 hover:bg-green-700" 
                    : "bg-blue-600 hover:bg-blue-700"
                } text-white`}
                size="sm"
                onClick={handleSelect}
                disabled={acknowledged || isSelected}
              >
                {getButtonText()}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StartupContactCard;